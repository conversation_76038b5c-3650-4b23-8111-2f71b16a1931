{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.1233752965927124, "root": {"boundingVolume": {"box": [9833.740234375, 4433.9033203125, -456.0411376953125, 148.43701171875, 0.0, 0.0, 0.0, 165.494873046875, 0.0, 0.0, 0.0, 88.37757873535156]}, "children": [{"boundingVolume": {"box": [9759.521484375, 4337.3642578125, -459.9028015136719, 74.21875, 0.0, 0.0, 0.0, 68.956298828125, 0.0, 0.0, 0.0, 84.51589965820312]}, "children": [{"boundingVolume": {"box": [9759.521484375, 4337.3642578125, -502.1842346191406, 74.21875, 0.0, 0.0, 0.0, 68.956298828125, 0.0, 0.0, 0.0, 42.010406494140625]}, "children": [{"boundingVolume": {"box": [9716.2265625, 4302.88623046875, -502.1842346191406, 30.92431640625, 0.0, 0.0, 0.0, 34.47802734375, 0.0, 0.0, 0.0, 42.010406494140625]}, "content": {"uri": "BlockA_L19_95.b3dm"}, "geometricError": 0.1578441858291626, "refine": "REPLACE"}, {"boundingVolume": {"box": [9716.2265625, 4371.8427734375, -500.0177307128906, 30.92431640625, 0.0, 0.0, 0.0, 34.478271484375, 0.0, 0.0, 0.0, 39.843902587890625]}, "content": {"uri": "BlockA_L19_94.b3dm"}, "geometricError": 0.1584196835756302, "refine": "REPLACE"}, {"boundingVolume": {"box": [9790.4453125, 4337.3642578125, -480.6551513671875, 43.29443359375, 0.0, 0.0, 0.0, 68.956298828125, 0.0, 0.0, 0.0, 20.4813232421875]}, "content": {"uri": "BlockA_L19_93.b3dm"}, "geometricError": 0.15187910199165344, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1890.b3dm"}, "geometricError": 0.3128620982170105, "refine": "REPLACE"}, {"boundingVolume": {"box": [9779.6806640625, 4337.3642578125, -417.7803649902344, 54.0595703125, 0.0, 0.0, 0.0, 68.956298828125, 0.0, 0.0, 0.0, 42.393463134765625]}, "children": [{"boundingVolume": {"box": [9779.83984375, 4302.88623046875, -424.95184326171875, 53.89990234375, 0.0, 0.0, 0.0, 34.47802734375, 0.0, 0.0, 0.0, 35.22198486328125]}, "content": {"uri": "BlockA_L19_92.b3dm"}, "geometricError": 0.14159905910491943, "refine": "REPLACE"}, {"boundingVolume": {"box": [9782.767578125, 4371.8427734375, -418.1634521484375, 50.97216796875, 0.0, 0.0, 0.0, 34.478271484375, 0.0, 0.0, 0.0, 42.01039123535156]}, "content": {"uri": "BlockA_L19_91.b3dm"}, "geometricError": 0.1411541849374771, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1889.b3dm"}, "geometricError": 0.282963365316391, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_982.b3dm"}, "geometricError": 0.5971683859825134, "refine": "REPLACE"}, {"boundingVolume": {"box": [9907.958984375, 4337.3642578125, -430.7187805175781, 74.21826171875, 0.0, 0.0, 0.0, 68.956298828125, 0.0, 0.0, 0.0, 63.05523681640625]}, "children": [{"boundingVolume": {"box": [9870.849609375, 4337.3642578125, -430.67987060546875, 37.109375, 0.0, 0.0, 0.0, 68.956298828125, 0.0, 0.0, 0.0, 63.01634216308594]}, "children": [{"boundingVolume": {"box": [9870.849609375, 4302.88623046875, -432.24755859375, 37.109375, 0.0, 0.0, 0.0, 34.47802734375, 0.0, 0.0, 0.0, 61.44865417480469]}, "content": {"uri": "BlockA_L19_90.b3dm"}, "geometricError": 0.1429852843284607, "refine": "REPLACE"}, {"boundingVolume": {"box": [9870.849609375, 4371.8427734375, -397.0670166015625, 37.109375, 0.0, 0.0, 0.0, 34.478271484375, 0.0, 0.0, 0.0, 29.403488159179688]}, "content": {"uri": "BlockA_L19_89.b3dm"}, "geometricError": 0.12961545586585999, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1888.b3dm"}, "geometricError": 0.2741166949272156, "refine": "REPLACE"}, {"boundingVolume": {"box": [9945.068359375, 4330.7978515625, -430.4204406738281, 37.10888671875, 0.0, 0.0, 0.0, 62.389892578125, 0.0, 0.0, 0.0, 62.75689697265625]}, "children": [{"boundingVolume": {"box": [9945.068359375, 4300.57470703125, -461.79888916015625, 37.10888671875, 0.0, 0.0, 0.0, 32.16650390625, 0.0, 0.0, 0.0, 31.378448486328125]}, "content": {"uri": "BlockA_L19_88.b3dm"}, "geometricError": 0.14657582342624664, "refine": "REPLACE"}, {"boundingVolume": {"box": [9945.068359375, 4330.7978515625, -399.0419921875, 37.10888671875, 0.0, 0.0, 0.0, 62.389892578125, 0.0, 0.0, 0.0, 31.378448486328125]}, "content": {"uri": "BlockA_L19_87.b3dm"}, "geometricError": 0.1333663910627365, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1887.b3dm"}, "geometricError": 0.2773456573486328, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_981.b3dm"}, "geometricError": 0.5514310598373413, "refine": "REPLACE"}, {"boundingVolume": {"box": [9765.8876953125, 4502.859375, -425.65435791015625, 80.5849609375, 0.0, 0.0, 0.0, 96.53857421875, 0.0, 0.0, 0.0, 57.99079895019531]}, "children": [{"boundingVolume": {"box": [9765.8876953125, 4446.544921875, -425.53387451171875, 80.5849609375, 0.0, 0.0, 0.0, 40.224365234375, 0.0, 0.0, 0.0, 57.870330810546875]}, "children": [{"boundingVolume": {"box": [9718.8798828125, 4446.544921875, -429.23565673828125, 33.5771484375, 0.0, 0.0, 0.0, 40.224365234375, 0.0, 0.0, 0.0, 54.15681457519531]}, "content": {"uri": "BlockA_L19_86.b3dm"}, "geometricError": 0.14068403840065002, "refine": "REPLACE"}, {"boundingVolume": {"box": [9799.46484375, 4446.544921875, -413.57080078125, 47.0078125, 0.0, 0.0, 0.0, 40.224365234375, 0.0, 0.0, 0.0, 45.90727233886719]}, "content": {"uri": "BlockA_L19_85.b3dm"}, "geometricError": 0.13170737028121948, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1886.b3dm"}, "geometricError": 0.27216285467147827, "refine": "REPLACE"}, {"boundingVolume": {"box": [9745.392578125, 4543.083984375, -413.2515869140625, 60.09033203125, 0.0, 0.0, 0.0, 56.314208984375, 0.0, 0.0, 0.0, 45.588043212890625]}, "children": [{"boundingVolume": {"box": [9710.2265625, 4543.083984375, -413.201904296875, 24.923828125, 0.0, 0.0, 0.0, 56.314208984375, 0.0, 0.0, 0.0, 45.538360595703125]}, "content": {"uri": "BlockA_L19_84.b3dm"}, "geometricError": 0.13412828743457794, "refine": "REPLACE"}, {"boundingVolume": {"box": [9770.04296875, 4526.1484375, -394.8938903808594, 34.89306640625, 0.0, 0.0, 0.0, 39.378662109375, 0.0, 0.0, 0.0, 27.2303466796875]}, "content": {"uri": "BlockA_L19_83.b3dm"}, "geometricError": 0.12780049443244934, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1885.b3dm"}, "geometricError": 0.26326966285705566, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_980.b3dm"}, "geometricError": 0.536825954914093, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_494.b3dm"}, "geometricError": 1.1233752965927124, "refine": "REPLACE"}}