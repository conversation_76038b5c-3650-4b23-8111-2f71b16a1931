import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermissions) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    
    if (!user) {
      throw new ForbiddenException('用户未认证');
    }

    const hasPermission = requiredPermissions.some(permission => {
      // 检查是否有超级权限
      if (user.permissions?.includes('*')) {
        return true;
      }
      
      // 检查具体权限
      return user.permissions?.includes(permission);
    });

    if (!hasPermission) {
      throw new ForbiddenException('权限不足');
    }

    return true;
  }
}