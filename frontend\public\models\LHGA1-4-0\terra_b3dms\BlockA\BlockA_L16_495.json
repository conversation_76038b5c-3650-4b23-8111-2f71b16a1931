{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.2001842260360718, "root": {"boundingVolume": {"box": [10860.3671875, 4069.814453125, -521.75927734375, 227.60107421875, 0.0, 0.0, 0.0, 198.59375, 0.0, 0.0, 0.0, 172.92884826660156]}, "children": [{"boundingVolume": {"box": [10708.00390625, 4069.814453125, -471.82421875, 75.23876953125, 0.0, 0.0, 0.0, 198.59375, 0.0, 0.0, 0.0, 122.99378967285156]}, "children": [{"boundingVolume": {"box": [10708.00390625, 3970.517578125, -494.24957275390625, 75.23876953125, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 100.56842041015625]}, "children": [{"boundingVolume": {"box": [10708.00390625, 3944.88818359375, -553.10791015625, 75.23876953125, 0.0, 0.0, 0.0, 73.6676025390625, 0.0, 0.0, 0.0, 41.71009826660156]}, "content": {"uri": "BlockA_L19_103.b3dm"}, "geometricError": 0.17206816375255585, "refine": "REPLACE"}, {"boundingVolume": {"box": [10708.00390625, 3999.224609375, -453.003662109375, 75.23876953125, 0.0, 0.0, 0.0, 70.5899658203125, 0.0, 0.0, 0.0, 58.394134521484375]}, "content": {"uri": "BlockA_L19_102.b3dm"}, "geometricError": 0.15311847627162933, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1894.b3dm"}, "geometricError": 0.32633230090141296, "refine": "REPLACE"}, {"boundingVolume": {"box": [10708.00390625, 4169.111328125, -412.780029296875, 75.23876953125, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 63.94963073730469]}, "children": [{"boundingVolume": {"box": [10670.384765625, 4119.462890625, -403.9903564453125, 37.619140625, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 54.501312255859375]}, "content": {"uri": "BlockA_L19_101.b3dm"}, "geometricError": 0.12838798761367798, "refine": "REPLACE"}, {"boundingVolume": {"box": [10745.623046875, 4119.462890625, -403.43048095703125, 37.61962890625, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 54.51530456542969]}, "content": {"uri": "BlockA_L19_100.b3dm"}, "geometricError": 0.12795491516590118, "refine": "REPLACE"}, {"boundingVolume": {"box": [10708.00390625, 4218.759765625, -417.008544921875, 75.23876953125, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 58.76039123535156]}, "content": {"uri": "BlockA_L19_99.b3dm"}, "geometricError": 0.13554193079471588, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1893.b3dm"}, "geometricError": 0.2635211944580078, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_984.b3dm"}, "geometricError": 0.5857499241828918, "refine": "REPLACE"}, {"boundingVolume": {"box": [10935.60546875, 4069.814453125, -523.5970458984375, 152.3623046875, 0.0, 0.0, 0.0, 198.59375, 0.0, 0.0, 0.0, 171.09107971191406]}, "children": [{"boundingVolume": {"box": [10934.896484375, 3970.517578125, -542.9683227539062, 151.65283203125, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 148.6802978515625]}, "children": [{"boundingVolume": {"box": [10820.86328125, 3970.517578125, -493.41864013671875, 37.61962890625, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 98.80912780761719]}, "content": {"uri": "BlockA_L19_98.b3dm"}, "geometricError": 0.16201256215572357, "refine": "REPLACE"}, {"boundingVolume": {"box": [10971.33984375, 3970.517578125, -577.300048828125, 112.85791015625, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 110.32070922851562]}, "content": {"uri": "BlockA_L18_4.b3dm"}, "geometricError": 0.18482615053653717, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1892.b3dm"}, "geometricError": 0.3403792977333069, "refine": "REPLACE"}, {"boundingVolume": {"box": [10866.474609375, 4169.111328125, -439.3574523925781, 83.23193359375, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 84.23812866210938]}, "children": [{"boundingVolume": {"box": [10865.3828125, 4127.7373046875, -441.77178955078125, 82.1396484375, 0.0, 0.0, 0.0, 57.923095703125, 0.0, 0.0, 0.0, 80.19764709472656]}, "content": {"uri": "BlockA_L19_97.b3dm"}, "geometricError": 0.13604910671710968, "refine": "REPLACE"}, {"boundingVolume": {"box": [10866.474609375, 4227.0341796875, -438.86859130859375, 83.23193359375, 0.0, 0.0, 0.0, 41.373779296875, 0.0, 0.0, 0.0, 50.22908020019531]}, "content": {"uri": "BlockA_L19_96.b3dm"}, "geometricError": 0.14451824128627777, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1891.b3dm"}, "geometricError": 0.28304290771484375, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_983.b3dm"}, "geometricError": 0.62306147813797, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_495.b3dm"}, "geometricError": 1.2001842260360718, "refine": "REPLACE"}}