import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
} from 'typeorm';
import { User } from './user.entity';

@Entity('roles', { schema: 'system' })
export class Role {
  @PrimaryGeneratedColumn('uuid')
  role_id: string;

  @Column({ length: 50, unique: true })
  name: string;

  @Column({ length: 100 })
  display_name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'jsonb', default: '[]' })
  permissions: string[] | string;

  @Column({ default: false })
  is_system: boolean;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  // 关联关系
  @ManyToMany(() => User, user => user.roles)
  users: User[];

  // 实例方法
  getPermissions(): string[] {
    if (!this.permissions) return [];
    
    return Array.isArray(this.permissions) 
      ? this.permissions 
      : JSON.parse(this.permissions as string);
  }

  hasPermission(permission: string): boolean {
    const rolePermissions = this.getPermissions();
    
    // 检查是否有超级权限
    if (rolePermissions.includes('*')) {
      return true;
    }
    
    // 检查具体权限
    return rolePermissions.includes(permission);
  }

  addPermission(permission: string): void {
    const permissions = this.getPermissions();
    if (!permissions.includes(permission)) {
      permissions.push(permission);
      this.permissions = permissions;
    }
  }

  removePermission(permission: string): void {
    const permissions = this.getPermissions();
    this.permissions = permissions.filter(p => p !== permission);
  }

  // 转换为安全的输出格式
  toSafeObject() {
    return {
      role_id: this.role_id,
      name: this.name,
      display_name: this.display_name,
      description: this.description,
      permissions: this.getPermissions(),
      is_system: this.is_system,
      created_at: this.created_at,
      updated_at: this.updated_at,
      user_count: this.users?.length || 0,
    };
  }
}