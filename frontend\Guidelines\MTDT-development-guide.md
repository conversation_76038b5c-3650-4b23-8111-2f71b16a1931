# 山思数字平台分组开发指南

## 1. 分组开发技术要求

### 1.1 团队分工建议

根据系统架构，建议按以下模块划分开发小组：

1. **地图核心组**：负责CesiumViewer组件和地图基础功能
2. **状态管理组**：负责Pinia Store设计和实现
3. **UI组件组**：负责通用UI组件和布局
4. **功能模块组**：负责各专业功能模块（测量、绘图等）
5. **无人机系统组**：负责无人机控制相关功能
6. **集成测试组**：负责系统集成和端到端测试

### 1.2 版本控制与分支管理

#### 1.2.1 Git工作流

采用Git Flow工作流：

- `main`：稳定的生产分支
- `develop`：开发主分支
- `feature/*`：功能开发分支
- `release/*`：发布准备分支
- `hotfix/*`：紧急修复分支

#### 1.2.2 分支命名规范

```
feature/[组件名]-[功能描述]
bugfix/[组件名]-[问题描述]
release/v[版本号]
```

#### 1.2.3 提交信息规范

采用Angular提交规范：

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型(type)包括：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码风格调整
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具变动

### 1.3 代码规范

#### 1.3.1 TypeScript规范

- 使用严格模式(`"strict": true`)
- 明确定义接口和类型
- 避免使用`any`类型
- 使用枚举代替魔术字符串

#### 1.3.2 Vue组件规范

- 使用组合式API (Composition API)
- 组件文件采用PascalCase命名
- Props定义明确类型和默认值
- 使用`<script setup>`语法
- 组件结构一致：`<script>`, `<template>`, `<style>`

#### 1.3.3 CSS规范

- 使用SCSS预处理器
- 采用BEM命名规范
- 组件样式使用scoped属性
- 主题变量统一管理

### 1.4 接口设计规范

#### 1.4.1 组件接口

- 明确定义Props和Events
- 提供完整的TypeScript类型
- 遵循单一职责原则
- 避免过度耦合

示例：
```typescript
// 组件Props定义示例
interface MapControlProps {
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showCompass?: boolean;
  showZoomControl?: boolean;
  theme?: 'light' | 'dark';
}

// 组件Events定义示例
interface MapControlEvents {
  onZoomChange: (level: number) => void;
  onCompassClick: () => void;
}
```

#### 1.4.2 模块接口

- 使用TypeScript接口定义模块API
- 提供详细的JSDoc注释
- 设计合理的错误处理机制
- 考虑异步操作的Promise返回

### 1.5 开发环境统一

#### 1.5.1 工具链

- Node.js版本：>=16.0.0
- 包管理器：npm或yarn（统一使用一种）
- 编辑器：VS Code（推荐）
- 插件：ESLint, Prettier, Volar

#### 1.5.2 开发环境配置

- ESLint配置统一
- Prettier格式化规则统一
- EditorConfig配置统一
- TypeScript配置统一

#### 1.5.3 依赖管理

- 使用固定版本号
- 定期更新依赖
- 避免引入过多小型依赖
- 核心库版本锁定

### 1.6 构建与部署

#### 1.6.1 构建流程

- 使用Vite构建工具
- 环境变量管理
- 资源优化策略
- 分包策略

#### 1.6.2 CI/CD集成

- 提交前代码检查
- 自动化测试
- 自动化构建
- 环境部署流程

## 2. 组件功能测试建议

### 2.1 测试策略概述

采用测试金字塔策略：
- 大量单元测试
- 适量组件测试
- 少量端到端测试

### 2.2 单元测试

#### 2.2.1 工具选择

- 测试框架：Vitest或Jest
- 断言库：Chai
- 覆盖率工具：Istanbul

#### 2.2.2 测试范围

- 工具类函数
- Store状态管理
- 复杂业务逻辑
- 数据转换函数

#### 2.2.3 最佳实践

- 测试文件与源文件同目录
- 命名规范：`*.spec.ts`或`*.test.ts`
- 使用Mock隔离外部依赖
- 关注边界条件测试

示例：
```typescript
// measureTools.test.ts
import { describe, it, expect } from 'vitest';
import { calculateDistance } from '../measureTools';

describe('MeasureTools', () => {
  describe('calculateDistance', () => {
    it('should calculate distance between two points correctly', () => {
      const point1 = { longitude: 104.0, latitude: 30.5 };
      const point2 = { longitude: 104.1, latitude: 30.6 };
      const distance = calculateDistance(point1, point2);
      expect(distance).toBeCloseTo(14.14, 1); // 约14.14公里
    });
    
    it('should return 0 for identical points', () => {
      const point = { longitude: 104.0, latitude: 30.5 };
      const distance = calculateDistance(point, point);
      expect(distance).toBe(0);
    });
  });
});
```

### 2.3 组件测试

#### 2.3.1 工具选择

- 测试库：Vue Test Utils
- 渲染库：@testing-library/vue
- 快照测试：Jest Snapshot

#### 2.3.2 测试范围

- 组件渲染
- 组件交互
- Props和Events
- 插槽和自定义指令

#### 2.3.3 最佳实践

- 测试组件公共API
- 避免测试内部实现
- 关注用户交互
- 使用快照测试UI变化

示例：
```typescript
// MapControls.spec.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import MapControls from '../MapControls.vue';

describe('MapControls', () => {
  it('renders correctly with default props', () => {
    const wrapper = mount(MapControls);
    expect(wrapper.find('.map-controls').exists()).toBe(true);
    expect(wrapper.find('.compass').exists()).toBe(true);
    expect(wrapper.find('.zoom-control').exists()).toBe(true);
  });
  
  it('emits zoom-in event when zoom in button is clicked', async () => {
    const wrapper = mount(MapControls);
    await wrapper.find('.zoom-in-button').trigger('click');
    expect(wrapper.emitted('zoom-in')).toBeTruthy();
    expect(wrapper.emitted('zoom-in')?.length).toBe(1);
  });
  
  it('hides compass when showCompass is false', () => {
    const wrapper = mount(MapControls, {
      props: {
        showCompass: false
      }
    });
    expect(wrapper.find('.compass').exists()).toBe(false);
  });
});
```

### 2.4 Cesium特定测试

#### 2.4.1 Cesium组件测试策略

- 使用Cesium Mock对象
- 分离Cesium依赖
- 测试Cesium交互逻辑

#### 2.4.2 Cesium Mock示例

```typescript
// cesium.mock.ts
export const mockViewer = {
  scene: {
    globe: {
      show: true
    },
    primitives: {
      add: vi.fn(),
      remove: vi.fn()
    }
  },
  entities: {
    add: vi.fn(),
    remove: vi.fn(),
    values: []
  },
  camera: {
    flyTo: vi.fn(),
    zoomIn: vi.fn(),
    zoomOut: vi.fn()
  }
};

// CesiumViewer.spec.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import CesiumViewer from '../CesiumViewer.vue';
import { mockViewer } from './cesium.mock';

// Mock Cesium全局对象
vi.mock('cesium', () => ({
  Viewer: vi.fn(() => mockViewer),
  // 其他需要的Cesium对象...
}));

describe('CesiumViewer', () => {
  beforeEach(() => {
    // 重置mock
    vi.clearAllMocks();
  });
  
  it('initializes Cesium viewer on mount', async () => {
    const wrapper = mount(CesiumViewer);
    await wrapper.vm.$nextTick();
    expect(Cesium.Viewer).toHaveBeenCalled();
  });
  
  // 更多测试...
});
```

### 2.5 集成测试

#### 2.5.1 工具选择

- Cypress
- Playwright

#### 2.5.2 测试范围

- 组件间交互
- 状态管理集成
- 路由导航
- 数据流

#### 2.5.3 最佳实践

- 使用测试数据
- 模拟API响应
- 关注用户流程
- 测试关键路径

### 2.6 端到端测试

#### 2.6.1 工具选择

- Cypress
- Playwright
- Selenium

#### 2.6.2 测试范围

- 核心用户流程
- 跨浏览器兼容性
- 性能测试
- 可访问性测试

#### 2.6.3 最佳实践

- 测试关键业务流程
- 使用页面对象模式
- 设置可靠的测试环境
- 处理异步操作

示例：
```javascript
// cypress/e2e/map-interaction.cy.js
describe('Map Interaction', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.get('.cesium-viewer').should('be.visible');
  });
  
  it('should zoom in when zoom-in button is clicked', () => {
    const initialHeight = cy.window().its('viewer.camera.positionCartographic.height');
    cy.get('.zoom-in-button').click();
    cy.window().its('viewer.camera.positionCartographic.height').should('be.lessThan', initialHeight);
  });
  
  it('should add a marker when map is clicked with marker tool active', () => {
    cy.get('.tool-button[data-tool="marker"]').click();
    cy.get('.cesium-viewer-canvas').click('center');
    cy.get('.marker-entity').should('have.length.at.least', 1);
  });
});
```

### 2.7 测试自动化

#### 2.7.1 CI集成

- GitHub Actions
- GitLab CI
- Jenkins

#### 2.7.2 测试报告

- 覆盖率报告
- 测试结果可视化
- 失败分析工具

#### 2.7.3 测试策略

- 提交前运行单元测试
- PR检查运行组件测试
- 夜间构建运行端到端测试
- 发布前运行全套测试

## 3. 文档规范

### 3.1 代码文档

- 使用JSDoc注释
- 组件Props文档
- 公共API文档
- 复杂算法说明

### 3.2 架构文档

- 组件关系图
- 数据流图
- 状态管理说明
- 扩展点文档

### 3.3 开发文档

- 环境搭建指南
- 开发流程说明
- 调试技巧
- 常见问题解答

## 4. 协作与沟通

### 4.1 任务管理

- 使用Jira或GitHub Issues
- 任务粒度适中
- 明确的完成标准
- 合理的工作量估计

### 4.2 代码审查

- 每个PR至少一个审查者
- 关注代码质量和一致性
- 使用自动化工具辅助
- 及时反馈和讨论

### 4.3 团队会议

- 每日站会
- 迭代计划会
- 代码评审会
- 技术分享会

## 5. 性能与优化

### 5.1 性能指标

- 首次加载时间
- 交互响应时间
- 内存占用
- 渲染性能

### 5.2 优化策略

- 代码分割
- 懒加载
- 资源优化
- 缓存策略

### 5.3 性能测试

- Lighthouse
- WebPageTest
- Chrome DevTools
- 自定义性能监控

## 6. 总结

分组开发WebGIS系统需要明确的技术规范、良好的团队协作和全面的测试策略。通过遵循本指南中的建议，团队可以提高开发效率，保证代码质量，并交付一个稳定、高性能的系统。

记住，规范和流程应该服务于项目和团队，而不是成为负担。根据项目进展和团队反馈，定期调整和优化开发流程和测试策略。