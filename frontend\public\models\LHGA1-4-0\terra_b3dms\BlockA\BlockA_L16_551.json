{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.259729027748108, "root": {"boundingVolume": {"box": [9631.1640625, 939.4515991210938, -974.3359985351562, 253.03466796875, 0.0, 0.0, 0.0, 123.47955322265625, 0.0, 0.0, 0.0, 135.66595458984375]}, "children": [{"boundingVolume": {"box": [9504.646484375, 939.4515991210938, -974.3359985351562, 126.51708984375, 0.0, 0.0, 0.0, 123.47955322265625, 0.0, 0.0, 0.0, 135.66595458984375]}, "children": [{"boundingVolume": {"box": [9451.931640625, 931.255126953125, -1011.7686767578125, 73.8017578125, 0.0, 0.0, 0.0, 115.2830810546875, 0.0, 0.0, 0.0, 37.0008544921875]}, "children": [{"boundingVolume": {"box": [9451.931640625, 854.2520751953125, -1009.1852416992188, 73.8017578125, 0.0, 0.0, 0.0, 38.279998779296875, 0.0, 0.0, 0.0, 34.41741943359375]}, "content": {"uri": "BlockA_L19_582.b3dm"}, "geometricError": 0.14703163504600525, "refine": "REPLACE"}, {"boundingVolume": {"box": [9484.099609375, 969.092041015625, -1011.7686767578125, 41.6337890625, 0.0, 0.0, 0.0, 76.55996704101562, 0.0, 0.0, 0.0, 37.0008544921875]}, "content": {"uri": "BlockA_L19_581.b3dm"}, "geometricError": 0.14659447968006134, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2130.b3dm"}, "geometricError": 0.29376834630966187, "refine": "REPLACE"}, {"boundingVolume": {"box": [9578.44921875, 938.810791015625, -1042.3671875, 52.71533203125, 0.0, 0.0, 0.0, 122.8387451171875, 0.0, 0.0, 0.0, 67.59942626953125]}, "children": [{"boundingVolume": {"box": [9578.44921875, 877.272705078125, -1061.859130859375, 52.71533203125, 0.0, 0.0, 0.0, 61.300628662109375, 0.0, 0.0, 0.0, 48.094818115234375]}, "content": {"uri": "BlockA_L19_580.b3dm"}, "geometricError": 0.1653200387954712, "refine": "REPLACE"}, {"boundingVolume": {"box": [9578.44921875, 999.8739013671875, -1015.2628784179688, 52.71533203125, 0.0, 0.0, 0.0, 61.300628662109375, 0.0, 0.0, 0.0, 40.49505615234375]}, "content": {"uri": "BlockA_L19_579.b3dm"}, "geometricError": 0.15526516735553741, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2129.b3dm"}, "geometricError": 0.3214424252510071, "refine": "REPLACE"}, {"boundingVolume": {"box": [9504.646484375, 947.3609008789062, -906.7567138671875, 126.51708984375, 0.0, 0.0, 0.0, 115.57025146484375, 0.0, 0.0, 0.0, 68.01107788085938]}, "children": [{"boundingVolume": {"box": [9420.302734375, 889.5758056640625, -936.321533203125, 42.17236328125, 0.0, 0.0, 0.0, 57.785125732421875, 0.0, 0.0, 0.0, 38.446258544921875]}, "content": {"uri": "BlockA_L19_578.b3dm"}, "geometricError": 0.13320814073085785, "refine": "REPLACE"}, {"boundingVolume": {"box": [9420.302734375, 1005.14599609375, -900.8240966796875, 42.17236328125, 0.0, 0.0, 0.0, 57.785125732421875, 0.0, 0.0, 0.0, 61.242431640625]}, "content": {"uri": "BlockA_L19_577.b3dm"}, "geometricError": 0.1729336380958557, "refine": "REPLACE"}, {"boundingVolume": {"box": [9546.8193359375, 990.1281127929688, -941.6527099609375, 84.3447265625, 0.0, 0.0, 0.0, 72.80303955078125, 0.0, 0.0, 0.0, 33.115142822265625]}, "content": {"uri": "BlockA_L18_933.b3dm"}, "geometricError": 0.19283801317214966, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2128.b3dm"}, "geometricError": 0.31749579310417175, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1098.b3dm"}, "geometricError": 0.6268289089202881, "refine": "REPLACE"}, {"boundingVolume": {"box": [9757.681640625, 939.4515991210938, -1010.113037109375, 126.517578125, 0.0, 0.0, 0.0, 123.47955322265625, 0.0, 0.0, 0.0, 98.16403198242188]}, "children": [{"boundingVolume": {"box": [9683.8798828125, 877.7117919921875, -1026.359130859375, 52.7158203125, 0.0, 0.0, 0.0, 61.739776611328125, 0.0, 0.0, 0.0, 63.6317138671875]}, "children": [{"boundingVolume": {"box": [9683.8798828125, 876.280029296875, -1058.175048828125, 52.7158203125, 0.0, 0.0, 0.0, 60.3079833984375, 0.0, 0.0, 0.0, 31.81585693359375]}, "content": {"uri": "BlockA_L19_576.b3dm"}, "geometricError": 0.1617424041032791, "refine": "REPLACE"}, {"boundingVolume": {"box": [9683.8798828125, 897.8494873046875, -994.5432739257812, 52.7158203125, 0.0, 0.0, 0.0, 41.60211181640625, 0.0, 0.0, 0.0, 31.81585693359375]}, "content": {"uri": "BlockA_L19_575.b3dm"}, "geometricError": 0.1478111892938614, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2127.b3dm"}, "geometricError": 0.3117675483226776, "refine": "REPLACE"}, {"boundingVolume": {"box": [9683.8798828125, 1001.19140625, -967.8019409179688, 52.7158203125, 0.0, 0.0, 0.0, 61.739776611328125, 0.0, 0.0, 0.0, 55.8529052734375]}, "children": [{"boundingVolume": {"box": [9683.8798828125, 1001.19140625, -967.8201904296875, 52.7158203125, 0.0, 0.0, 0.0, 61.739776611328125, 0.0, 0.0, 0.0, 55.678375244140625]}, "content": {"uri": "BlockA_L19_574.b3dm"}, "geometricError": 0.159499853849411, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2126.b3dm"}, "geometricError": 0.32226383686065674, "refine": "REPLACE"}, {"boundingVolume": {"box": [9810.3974609375, 939.4515991210938, -1026.2958984375, 73.8017578125, 0.0, 0.0, 0.0, 123.47955322265625, 0.0, 0.0, 0.0, 81.89569091796875]}, "children": [{"boundingVolume": {"box": [9773.49609375, 877.7117919921875, -1015.8155517578125, 36.90087890625, 0.0, 0.0, 0.0, 61.739776611328125, 0.0, 0.0, 0.0, 45.199249267578125]}, "content": {"uri": "BlockA_L19_573.b3dm"}, "geometricError": 0.1528703272342682, "refine": "REPLACE"}, {"boundingVolume": {"box": [9847.298828125, 877.7117919921875, -1070.32666015625, 36.90087890625, 0.0, 0.0, 0.0, 61.739776611328125, 0.0, 0.0, 0.0, 37.75042724609375]}, "content": {"uri": "BlockA_L19_572.b3dm"}, "geometricError": 0.16861219704151154, "refine": "REPLACE"}, {"boundingVolume": {"box": [9810.3974609375, 1001.19140625, -1019.3341064453125, 73.8017578125, 0.0, 0.0, 0.0, 61.739776611328125, 0.0, 0.0, 0.0, 73.32260131835938]}, "content": {"uri": "BlockA_L19_571.b3dm"}, "geometricError": 0.15283386409282684, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2125.b3dm"}, "geometricError": 0.3139796555042267, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1097.b3dm"}, "geometricError": 0.631683886051178, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_551.b3dm"}, "geometricError": 1.259729027748108, "refine": "REPLACE"}}