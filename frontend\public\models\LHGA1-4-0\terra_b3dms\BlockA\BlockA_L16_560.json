{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.1463104486465454, "root": {"boundingVolume": {"box": [8329.84375, 883.1988525390625, -881.4842529296875, 108.443359375, 0.0, 0.0, 0.0, 138.28286743164062, 0.0, 0.0, 0.0, 116.99197387695312]}, "children": [{"boundingVolume": {"box": [8329.84375, 802.5338745117188, -910.8912353515625, 108.443359375, 0.0, 0.0, 0.0, 57.61785888671875, 0.0, 0.0, 0.0, 87.29544067382812]}, "children": [{"boundingVolume": {"box": [8275.6220703125, 802.5338745117188, -925.2359619140625, 54.2216796875, 0.0, 0.0, 0.0, 57.61785888671875, 0.0, 0.0, 0.0, 71.21023559570312]}, "children": [{"boundingVolume": {"box": [8275.6220703125, 802.5338745117188, -959.8095703125, 54.2216796875, 0.0, 0.0, 0.0, 57.61785888671875, 0.0, 0.0, 0.0, 35.018096923828125]}, "content": {"uri": "BlockA_L19_651.b3dm"}, "geometricError": 0.13329951465129852, "refine": "REPLACE"}, {"boundingVolume": {"box": [8278.2958984375, 810.4627075195312, -889.7734375, 51.5478515625, 0.0, 0.0, 0.0, 49.68902587890625, 0.0, 0.0, 0.0, 35.018096923828125]}, "content": {"uri": "BlockA_L19_650.b3dm"}, "geometricError": 0.11926066130399704, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2162.b3dm"}, "geometricError": 0.2522992193698883, "refine": "REPLACE"}, {"boundingVolume": {"box": [8384.0654296875, 802.5338745117188, -893.56201171875, 54.2216796875, 0.0, 0.0, 0.0, 57.61785888671875, 0.0, 0.0, 0.0, 67.13772583007812]}, "children": [{"boundingVolume": {"box": [8383.09765625, 786.6558837890625, -921.828369140625, 53.25390625, 0.0, 0.0, 0.0, 41.739837646484375, 0.0, 0.0, 0.0, 38.871429443359375]}, "content": {"uri": "BlockA_L19_649.b3dm"}, "geometricError": 0.1206529438495636, "refine": "REPLACE"}, {"boundingVolume": {"box": [8384.0654296875, 778.5264282226562, -867.1398315429688, 54.2216796875, 0.0, 0.0, 0.0, 33.61041259765625, 0.0, 0.0, 0.0, 15.81707763671875]}, "content": {"uri": "BlockA_L19_648.b3dm"}, "geometricError": 0.11141534894704819, "refine": "REPLACE"}, {"boundingVolume": {"box": [8384.0654296875, 836.144287109375, -855.1915893554688, 54.2216796875, 0.0, 0.0, 0.0, 24.0074462890625, 0.0, 0.0, 0.0, 27.76531982421875]}, "content": {"uri": "BlockA_L19_647.b3dm"}, "geometricError": 0.11016584187746048, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2161.b3dm"}, "geometricError": 0.23050598800182343, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1115.b3dm"}, "geometricError": 0.48262229561805725, "refine": "REPLACE"}, {"boundingVolume": {"box": [8329.84375, 940.8167724609375, -847.1192626953125, 108.443359375, 0.0, 0.0, 0.0, 80.66500854492188, 0.0, 0.0, 0.0, 82.600830078125]}, "children": [{"boundingVolume": {"box": [8275.6220703125, 940.8167724609375, -847.62939453125, 54.2216796875, 0.0, 0.0, 0.0, 80.66500854492188, 0.0, 0.0, 0.0, 82.09072875976562]}, "children": [{"boundingVolume": {"box": [8275.6220703125, 893.7621459960938, -887.2392578125, 54.2216796875, 0.0, 0.0, 0.0, 33.61041259765625, 0.0, 0.0, 0.0, 42.4808349609375]}, "content": {"uri": "BlockA_L19_646.b3dm"}, "geometricError": 0.11336452513933182, "refine": "REPLACE"}, {"boundingVolume": {"box": [8275.6220703125, 974.4271240234375, -832.9517822265625, 54.2216796875, 0.0, 0.0, 0.0, 47.054595947265625, 0.0, 0.0, 0.0, 64.34713745117188]}, "content": {"uri": "BlockA_L18_1079.b3dm"}, "geometricError": 0.23015718162059784, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2160.b3dm"}, "geometricError": 0.3359026312828064, "refine": "REPLACE"}, {"boundingVolume": {"box": [8384.0654296875, 940.8167724609375, -816.604248046875, 54.2216796875, 0.0, 0.0, 0.0, 80.66500854492188, 0.0, 0.0, 0.0, 51.065643310546875]}, "children": [{"boundingVolume": {"box": [8384.0654296875, 893.7621459960938, -829.5519409179688, 54.2216796875, 0.0, 0.0, 0.0, 33.61041259765625, 0.0, 0.0, 0.0, 38.08416748046875]}, "content": {"uri": "BlockA_L19_645.b3dm"}, "geometricError": 0.13641142845153809, "refine": "REPLACE"}, {"boundingVolume": {"box": [8384.0654296875, 974.4271240234375, -816.267333984375, 54.2216796875, 0.0, 0.0, 0.0, 47.054595947265625, 0.0, 0.0, 0.0, 49.689361572265625]}, "content": {"uri": "BlockA_L18_1078.b3dm"}, "geometricError": 0.24387893080711365, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_578.b3dm"}, "geometricError": 0.37223803997039795, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1114.b3dm"}, "geometricError": 0.7065878510475159, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_560.b3dm"}, "geometricError": 1.1463104486465454, "refine": "REPLACE"}}