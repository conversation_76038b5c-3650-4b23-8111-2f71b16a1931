# 山思数字平台运维手册

## 1. 系统架构

### 1.1 部署架构

```mermaid
graph TD
    A[负载均衡器] --> B1[前端服务器1]
    A --> B2[前端服务器2]
    A --> C1[API服务器1]
    A --> C2[API服务器2]
    C1 --> D1[主数据库]
    C2 --> D1
    D1 --> D2[从数据库1]
    D1 --> D3[从数据库2]
    C1 --> E1[Redis集群]
    C2 --> E1
```

### 1.2 网络架构

```yaml
networks:
  dmz:
    subnet: ***********/24
    description: 外部访问区域
    
  app:
    subnet: ***********/24
    description: 应用服务区域
    
  data:
    subnet: ***********/24
    description: 数据库区域
```

## 2. 日常运维

### 2.1 系统监控

#### 2.1.1 监控指标
```yaml
# 系统指标
system_metrics:
  - cpu_usage
  - memory_usage
  - disk_usage
  - network_io
  - system_load

# 应用指标
application_metrics:
  - response_time
  - error_rate
  - request_count
  - active_users
  - concurrent_connections

# 数据库指标
database_metrics:
  - query_performance
  - connection_count
  - buffer_usage
  - replication_lag
  - transaction_rate
```

#### 2.1.2 监控配置
```yaml
# Prometheus配置
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'application'
    static_configs:
      - targets: ['localhost:3000']

  - job_name: 'database'
    static_configs:
      - targets: ['localhost:9187']
```

### 2.2 日志管理

#### 2.2.1 日志收集
```yaml
# Filebeat配置
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/nginx/*.log
    - /var/log/application/*.log
    - /var/log/postgresql/*.log

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "logs-%{[agent.version]}-%{+yyyy.MM.dd}"
```

#### 2.2.2 日志分析
```yaml
# Logstash过滤器
filter {
  if [type] == "nginx" {
    grok {
      match => { "message" => "%{COMBINEDAPACHELOG}" }
    }
  }
  
  if [type] == "application" {
    json {
      source => "message"
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
    target => "@timestamp"
  }
}
```

### 2.3 备份管理

#### 2.3.1 数据库备份
```bash
#!/bin/bash
# 数据库备份脚本

# 配置
BACKUP_DIR="/data/backups/db"
DB_NAME="mtdt"
RETENTION_DAYS=7

# 创建备份
pg_dump -Fc $DB_NAME > "$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).dump"

# 清理旧备份
find $BACKUP_DIR -type f -mtime +$RETENTION_DAYS -delete
```

#### 2.3.2 文件备份
```bash
#!/bin/bash
# 文件备份脚本

# 配置
BACKUP_DIR="/data/backups/files"
SOURCE_DIRS=("/data/uploads" "/data/static")
RETENTION_DAYS=7

# 创建备份
tar czf "$BACKUP_DIR/files_$(date +%Y%m%d_%H%M%S).tar.gz" ${SOURCE_DIRS[@]}

# 清理旧备份
find $BACKUP_DIR -type f -mtime +$RETENTION_DAYS -delete
```

## 3. 监控告警

### 3.1 告警规则

#### 3.1.1 系统告警
```yaml
# Prometheus告警规则
groups:
- name: system
  rules:
  - alert: HighCPUUsage
    expr: avg(cpu_usage_percent) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High CPU usage detected
      
  - alert: LowDiskSpace
    expr: disk_free_percent < 10
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: Low disk space detected
```

#### 3.1.2 应用告警
```yaml
# 应用告警规则
groups:
- name: application
  rules:
  - alert: HighErrorRate
    expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: High error rate detected
      
  - alert: SlowResponses
    expr: http_request_duration_seconds{quantile="0.9"} > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Slow response times detected
```

### 3.2 告警通知

#### 3.2.1 通知渠道
```yaml
# Alertmanager配置
global:
  resolve_timeout: 5m

route:
  group_by: ['alertname']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'ops-team'

receivers:
- name: 'ops-team'
  email_configs:
  - to: '<EMAIL>'
  webhook_configs:
  - url: 'http://webhook.mtdt.com/alert'
```

#### 3.2.2 告警升级
```yaml
# 告警升级策略
route:
  routes:
  - match:
      severity: critical
    receiver: 'urgent-ops'
    repeat_interval: 1h
    
  - match:
      severity: warning
    receiver: 'ops-team'
    repeat_interval: 4h

receivers:
- name: 'urgent-ops'
  email_configs:
  - to: '<EMAIL>'
  webhook_configs:
  - url: 'http://webhook.mtdt.com/urgent'
```

## 4. 故障处理

### 4.1 故障诊断

#### 4.1.1 系统诊断
```bash
#!/bin/bash
# 系统诊断脚本

echo "=== 系统状态检查 ==="
date

echo "--- CPU使用情况 ---"
top -bn1 | head -n 5

echo "--- 内存使用情况 ---"
free -m

echo "--- 磁盘使用情况 ---"
df -h

echo "--- 网络连接状态 ---"
netstat -an | grep ESTABLISHED | wc -l

echo "--- 系统日志检查 ---"
tail -n 50 /var/log/syslog
```

#### 4.1.2 应用诊断
```bash
#!/bin/bash
# 应用诊断脚本

echo "=== 应用状态检查 ==="

echo "--- 进程状态 ---"
ps aux | grep node

echo "--- 日志检查 ---"
tail -n 100 /var/log/application/error.log

echo "--- 端口检查 ---"
netstat -tlnp

echo "--- 数据库连接 ---"
psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;"
```

### 4.2 故障处理流程

#### 4.2.1 处理步骤
```mermaid
graph TD
    A[故障发现] --> B[初步诊断]
    B --> C[确定影响范围]
    C --> D[制定解决方案]
    D --> E[实施修复]
    E --> F[验证恢复]
    F --> G[事后总结]
```

#### 4.2.2 应急预案
```yaml
# 常见故障处理预案
scenarios:
  service_unavailable:
    - check_process_status
    - restart_service
    - check_logs
    - notify_team
    
  database_error:
    - check_connection
    - check_replication
    - failover_if_needed
    - restore_backup
    
  high_load:
    - identify_bottleneck
    - scale_resources
    - optimize_queries
    - adjust_caching
```

## 5. 性能优化

### 5.1 系统优化

#### 5.1.1 系统参数
```bash
# 系统参数优化
cat >> /etc/sysctl.conf << EOF
# 网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_tw_reuse = 1

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 60
vm.dirty_background_ratio = 2

# 文件系统
fs.file-max = 2097152
fs.nr_open = 2097152
EOF

sysctl -p
```

#### 5.1.2 资源限制
```bash
# 资源限制配置
cat >> /etc/security/limits.conf << EOF
* soft nofile 65535
* hard nofile 65535
* soft nproc 65535
* hard nproc 65535
EOF
```

### 5.2 应用优化

#### 5.2.1 Node.js优化
```javascript
// PM2配置
module.exports = {
  apps: [{
    name: 'mtdt-api',
    script: 'dist/main.js',
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      NODE_OPTIONS: '--max-old-space-size=4096'
    }
  }]
}
```

#### 5.2.2 Nginx优化
```nginx
# Nginx配置优化
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 65535;
    use epoll;
    multi_accept on;
}

http {
    keepalive_timeout 65;
    keepalive_requests 100;
    
    client_max_body_size 10m;
    client_body_buffer_size 128k;
    
    proxy_buffer_size 4k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;
    
    gzip on;
    gzip_types text/plain text/css application/json application/javascript;
}
```

## 6. 安全维护

### 6.1 安全检查

#### 6.1.1 系统安全
```bash
#!/bin/bash
# 安全检查脚本

echo "=== 安全检查 ==="

echo "--- 系统更新状态 ---"
apt list --upgradable

echo "--- 开放端口检查 ---"
netstat -tlnp

echo "--- 登录记录检查 ---"
last | head -n 10

echo "--- 失败登录检查 ---"
grep "Failed password" /var/log/auth.log

echo "--- 进程检查 ---"
ps aux | grep -v [p]s | sort -rn -k 3 | head
```

#### 6.1.2 应用安全
```bash
#!/bin/bash
# 应用安全检查

echo "=== 应用安全检查 ==="

echo "--- 依赖包检查 ---"
npm audit

echo "--- SSL证书检查 ---"
openssl x509 -noout -dates -in /etc/nginx/ssl/mtdt.crt

echo "--- 文件权限检查 ---"
find /data -type f -perm /o+w

echo "--- 配置文件检查 ---"
grep -r "password" /etc/mtdt/
```

### 6.2 安全更新

#### 6.2.1 系统更新
```bash
#!/bin/bash
# 系统更新脚本

# 更新包列表
apt update

# 安装安全更新
apt upgrade -y

# 清理旧包
apt autoremove -y
apt clean

# 检查是否需要重启
if [ -f /var/run/reboot-required ]; then
    echo "系统需要重启"
fi
```

#### 6.2.2 应用更新
```bash
#!/bin/bash
# 应用更新脚本

# 备份当前版本
cp -r /opt/mtdt /opt/mtdt_backup_$(date +%Y%m%d)

# 更新代码
cd /opt/mtdt
git pull origin main

# 更新依赖
npm ci

# 构建应用
npm run build

# 重启服务
pm2 reload mtdt-api

# 验证服务状态
curl -f http://localhost:3001/health
```

## 7. 容量规划

### 7.1 资源监控

#### 7.1.1 监控指标
```yaml
# 容量监控指标
metrics:
  system:
    - cpu_usage_percent
    - memory_usage_percent
    - disk_usage_percent
    - network_bandwidth_usage
    
  application:
    - concurrent_users
    - request_rate
    - response_time
    - error_rate
    
  database:
    - connection_count
    - query_performance
    - storage_usage
    - index_size
```

#### 7.1.2 趋势分析
```python
# 容量趋势分析脚本
import pandas as pd
import numpy as np

def analyze_trends(metrics_data, days=30):
    df = pd.DataFrame(metrics_data)
    
    # 计算增长率
    growth_rate = df.pct_change().mean() * 100
    
    # 预测未来使用量
    forecast = df.ewm(span=7).mean().tail(1) * (1 + growth_rate)
    
    return {
        'current_usage': df.tail(1).to_dict(),
        'growth_rate': growth_rate.to_dict(),
        'forecast': forecast.to_dict()
    }
```

### 7.2 扩容策略

#### 7.2.1 触发条件
```yaml
# 扩容触发条件
scaling_triggers:
  cpu:
    threshold: 80
    duration: 5m
    action: scale_up
    
  memory:
    threshold: 85
    duration: 5m
    action: scale_up
    
  disk:
    threshold: 90
    duration: 1h
    action: add_storage
```

#### 7.2.2 扩容方案
```yaml
# 扩容方案
scaling_plans:
  application:
    current_size: 2
    max_size: 6
    step: 1
    cooldown: 300
    
  database:
    storage:
      increment: 100GB
      max_size: 2TB
    replicas:
      current: 2
      max: 3
```

## 8. 运维自动化

### 8.1 自动化脚本

#### 8.1.1 部署脚本
```bash
#!/bin/bash
# 自动化部署脚本

# 配置
APP_NAME="mtdt"
DEPLOY_DIR="/opt/$APP_NAME"
BACKUP_DIR="/data/backups/$APP_NAME"

# 创建备份
create_backup() {
    echo "Creating backup..."
    mkdir -p $BACKUP_DIR
    tar czf "$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).tar.gz" $DEPLOY_DIR
}

# 更新代码
update_code() {
    echo "Updating code..."
    cd $DEPLOY_DIR
    git pull origin main
}

# 更新依赖
update_dependencies() {
    echo "Updating dependencies..."
    npm ci
}

# 构建应用
build_app() {
    echo "Building application..."
    npm run build
}

# 重启服务
restart_service() {
    echo "Restarting service..."
    pm2 reload $APP_NAME
}

# 主流程
main() {
    create_backup
    update_code
    update_dependencies
    build_app
    restart_service
}

main
```

#### 8.1.2 监控脚本
```python
#!/usr/bin/env python3
# 自动化监控脚本

import psutil
import requests
import time
import json

def collect_metrics():
    metrics = {
        'cpu': psutil.cpu_percent(interval=1),
        'memory': psutil.virtual_memory().percent,
        'disk': psutil.disk_usage('/').percent,
        'network': {
            'sent': psutil.net_io_counters().bytes_sent,
            'recv': psutil.net_io_counters().bytes_recv
        }
    }
    return metrics

def check_application():
    try:
        response = requests.get('http://localhost:3001/health')
        return response.status_code == 200
    except:
        return False

def send_alert(message):
    webhook_url = 'http://alert.mtdt.com/webhook'
    payload = {
        'text': message,
        'timestamp': time.time()
    }
    requests.post(webhook_url, json=payload)

def main():
    while True:
        metrics = collect_metrics()
        app_healthy = check_application()
        
        if metrics['cpu'] > 80:
            send_alert('High CPU usage detected')
            
        if not app_healthy:
            send_alert('Application is not responding')
            
        time.sleep(60)

if __name__ == '__main__':
    main()
```

### 8.2 自动化工具

#### 8.2.1 Ansible配置
```yaml
# ansible/playbook.yml
---
- name: Deploy MTDT Application
  hosts: mtdt_servers
  become: yes
  
  tasks:
    - name: Update system packages
      apt:
        update_cache: yes
        upgrade: yes
        
    - name: Install required packages
      apt:
        name: "{{ item }}"
        state: present
      loop:
        - nginx
        - nodejs
        - postgresql
        
    - name: Deploy application
      git:
        repo: "{{ repo_url }}"
        dest: "{{ app_path }}"
        version: "{{ branch }}"
      notify: restart application
        
  handlers:
    - name: restart application
      service:
        name: mtdt
        state: restarted
```

#### 8.2.2 Jenkins Pipeline
```groovy
// Jenkinsfile
pipeline {
    agent any
    
    environment {
        APP_NAME = 'mtdt'
        DEPLOY_DIR = '/opt/mtdt'
    }
    
    stages {
        stage('Prepare') {
            steps {
                checkout scm
            }
        }
        
        stage('Build') {
            steps {
                sh 'npm ci'
                sh 'npm run build'
            }
        }
        
        stage('Test') {
            steps {
                sh 'npm test'
            }
        }
        
        stage('Deploy') {
            steps {
                sh './scripts/deploy.sh'
            }
        }
        
        stage('Verify') {
            steps {
                sh './scripts/verify.sh'
            }
        }
    }
    
    post {
        success {
            slackSend channel: '#ops', 
                      color: 'good', 
                      message: "部署成功: ${env.JOB_NAME} ${env.BUILD_NUMBER}"
        }
        failure {
            slackSend channel: '#ops', 
                      color: 'danger', 
                      message: "部署失败: ${env.JOB_NAME} ${env.BUILD_NUMBER}"
        }
    }
}
```

## 9. 应急预案

### 9.1 故障应急

#### 9.1.1 服务中断
```yaml
# 服务中断应急预案
steps:
  - name: 初步诊断
    actions:
      - 检查服务进程状态
      - 检查系统资源使用情况
      - 检查日志文件
      
  - name: 快速恢复
    actions:
      - 重启服务进程
      - 切换到备用节点
      - 回滚到上一个稳定版本
      
  - name: 根因分析
    actions:
      - 收集故障时的监控数据
      - 分析日志信息
      - 复现问题场景
      
  - name: 长期解决
    actions:
      - 修复根本原因
      - 更新监控告警规则
      - 完善应急预案
```

#### 9.1.2 数据丢失
```yaml
# 数据丢失应急预案
steps:
  - name: 确认范围
    actions:
      - 确定丢失数据的范围
      - 评估影响程度
      - 通知相关人员
      
  - name: 数据恢复
    actions:
      - 停止相关服务
      - 恢复最近的备份
      - 重放事务日志
      
  - name: 验证数据
    actions:
      - 检查数据完整性
      - 验证业务功能
      - 确认数据一致性
      
  - name: 预防措施
    actions:
      - 优化备份策略
      - 加强数据安全
      - 完善操作流程
```

### 9.2 灾难恢复

#### 9.2.1 恢复流程
```mermaid
graph TD
    A[灾难发生] --> B[评估影响]
    B --> C[启动备用系统]
    C --> D[数据恢复]
    D --> E[功能验证]
    E --> F[切换流量]
    F --> G[监控稳定性]
```

#### 9.2.2 恢复演练
```yaml
# 灾难恢复演练计划
drills:
  - name: 数据中心故障
    frequency: 每季度
    steps:
      - 模拟主数据中心断电
      - 启动备用数据中心
      - 验证数据同步
      - 测试业务功能
      
  - name: 数据库故障
    frequency: 每月
    steps:
      - 模拟主库故障
      - 触发自动故障转移
      - 验证数据一致性
      - 测试应用连接
```

## 10. 总结

本运维手册提供了山思数字平台的完整运维方案，包括：

1. **日常运维**
   - 系统监控
   - 日志管理
   - 备份管理

2. **故障处理**
   - 监控告警
   - 故障诊断
   - 应急预案

3. **系统优化**
   - 性能优化
   - 安全维护
   - 容量规划

4. **自动化运维**
   - 自动化脚本
   - 自动化工具
   - 持续集成

建议定期审查和更新本手册，确保运维工作的规范性和有效性。