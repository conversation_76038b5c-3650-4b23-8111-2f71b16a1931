import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('audit_logs', { schema: 'system' })
export class AuditLog {
  @PrimaryGeneratedColumn('uuid')
  log_id: string;

  @Column({ type: 'uuid', nullable: true })
  user_id: string;

  @Column({ length: 100 })
  action: string;

  @Column({ length: 100, nullable: true })
  resource: string;

  @Column({ length: 100, nullable: true })
  resource_id: string;

  @Column({ type: 'jsonb', nullable: true })
  old_values: any;

  @Column({ type: 'jsonb', nullable: true })
  new_values: any;

  @Column({ type: 'inet', nullable: true })
  ip_address: string;

  @Column({ type: 'text', nullable: true })
  user_agent: string;

  @Column({ type: 'text', nullable: true })
  details: string;

  @Column({ length: 50, nullable: true })
  resourceType: string;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  // 关联关系
  @ManyToOne(() => User, user => user.audit_logs)
  @JoinColumn({ name: 'user_id' })
  user: User;

  // 驼峰命名访问器
  get id(): string {
    return this.log_id;
  }

  get userId(): string {
    return this.user_id;
  }

  set userId(value: string) {
    this.user_id = value;
  }

  get resourceId(): string {
    return this.resource_id;
  }

  set resourceId(value: string) {
    this.resource_id = value;
  }

  get oldValues(): any {
    return this.old_values;
  }

  set oldValues(value: any) {
    this.old_values = value;
  }

  get newValues(): any {
    return this.new_values;
  }

  set newValues(value: any) {
    this.new_values = value;
  }

  get ipAddress(): string {
    return this.ip_address;
  }

  set ipAddress(value: string) {
    this.ip_address = value;
  }

  get userAgent(): string {
    return this.user_agent;
  }

  set userAgent(value: string) {
    this.user_agent = value;
  }

  get timestamp(): Date {
    return this.created_at;
  }

  // 静态方法 - 创建审计日志
  static createLog(data: {
    user_id?: string;
    action: string;
    resource?: string;
    resource_id?: string;
    old_values?: any;
    new_values?: any;
    ip_address?: string;
    user_agent?: string;
  }): AuditLog {
    const log = new AuditLog();
    Object.assign(log, data);
    return log;
  }

  // 转换为安全的输出格式
  toSafeObject() {
    return {
      log_id: this.log_id,
      user_id: this.user_id,
      action: this.action,
      resource: this.resource,
      resource_id: this.resource_id,
      old_values: this.old_values,
      new_values: this.new_values,
      ip_address: this.ip_address,
      user_agent: this.user_agent,
      created_at: this.created_at,
      user: this.user ? {
        user_id: this.user.user_id,
        username: this.user.username,
        full_name: this.user.full_name,
      } : null,
    };
  }
}