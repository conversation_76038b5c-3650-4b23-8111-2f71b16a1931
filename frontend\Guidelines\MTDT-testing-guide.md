# 山思数字平台测试规范

## 1. 测试策略

### 1.1 测试层次

```mermaid
graph TD
    A[单元测试] --> B[集成测试]
    B --> C[系统测试]
    C --> D[验收测试]
    
    E[前端测试] --> F[组件测试]
    F --> G[E2E测试]
    
    H[后端测试] --> I[API测试]
    I --> J[性能测试]
```

### 1.2 测试类型

1. **功能测试**
   - 单元测试
   - 集成测试
   - 端到端测试
   - 验收测试

2. **非功能测试**
   - 性能测试
   - 安全测试
   - 兼容性测试
   - 可用性测试

### 1.3 测试环境

```yaml
# 开发环境
development:
  url: http://dev.mtdt.com
  api: http://dev-api.mtdt.com
  database: dev-db.mtdt.com
  redis: dev-redis.mtdt.com

# 测试环境
testing:
  url: http://test.mtdt.com
  api: http://test-api.mtdt.com
  database: test-db.mtdt.com
  redis: test-redis.mtdt.com

# 预生产环境
staging:
  url: http://staging.mtdt.com
  api: http://staging-api.mtdt.com
  database: staging-db.mtdt.com
  redis: staging-redis.mtdt.com
```

## 2. 前端测试

### 2.1 单元测试

#### 2.1.1 工具配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'tests/'],
    },
  },
});
```

#### 2.1.2 测试示例
```typescript
// utils.test.ts
import { describe, it, expect } from 'vitest';
import { formatCoordinates } from '@/utils/coordinates';

describe('formatCoordinates', () => {
  it('should format coordinates correctly', () => {
    const coords = [120.5, 30.5];
    expect(formatCoordinates(coords)).toBe('120.5°E, 30.5°N');
  });

  it('should handle negative coordinates', () => {
    const coords = [-120.5, -30.5];
    expect(formatCoordinates(coords)).toBe('120.5°W, 30.5°S');
  });
});
```

### 2.2 组件测试

#### 2.2.1 组件测试配置
```typescript
// test/components/setup.ts
import { config } from '@vue/test-utils';
import { createPinia } from 'pinia';

const pinia = createPinia();

config.global.plugins = [pinia];
```

#### 2.2.2 组件测试示例
```typescript
// MapControl.test.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import MapControl from '@/components/MapControl.vue';

describe('MapControl', () => {
  it('renders zoom controls', () => {
    const wrapper = mount(MapControl);
    expect(wrapper.find('.zoom-in').exists()).toBe(true);
    expect(wrapper.find('.zoom-out').exists()).toBe(true);
  });

  it('emits zoom events', async () => {
    const wrapper = mount(MapControl);
    await wrapper.find('.zoom-in').trigger('click');
    expect(wrapper.emitted('zoom')?.[0]).toEqual([1]);
  });
});
```

### 2.3 E2E测试

#### 2.3.1 Cypress配置
```javascript
// cypress.config.js
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: false,
    screenshotOnRunFailure: true,
    supportFile: 'cypress/support/e2e.ts',
  },
  env: {
    apiUrl: 'http://localhost:3001',
  },
});
```

#### 2.3.2 E2E测试示例
```typescript
// cypress/e2e/map.cy.ts
describe('Map Functionality', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should load map correctly', () => {
    cy.get('#map-container').should('be.visible');
    cy.get('.cesium-viewer').should('exist');
  });

  it('should handle layer controls', () => {
    cy.get('.layer-control').click();
    cy.get('.layer-list').should('be.visible');
    cy.get('.layer-item').first().click();
  });
});
```

## 3. 后端测试

### 3.1 单元测试

#### 3.1.1 Jest配置
```typescript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

#### 3.1.2 服务测试示例
```typescript
// LayerService.test.ts
import { describe, it, expect, beforeEach } from '@jest/globals';
import { LayerService } from '@/services/LayerService';
import { createMockLayer } from '@/tests/mocks';

describe('LayerService', () => {
  let service: LayerService;

  beforeEach(() => {
    service = new LayerService();
  });

  it('should create layer', async () => {
    const layer = createMockLayer();
    const result = await service.createLayer(layer);
    expect(result).toHaveProperty('id');
    expect(result.name).toBe(layer.name);
  });
});
```

### 3.2 API测试

#### 3.2.1 Supertest配置
```typescript
// test/setup.ts
import { app } from '../src/app';
import supertest from 'supertest';

export const request = supertest(app);
```

#### 3.2.2 API测试示例
```typescript
// layers.test.ts
import { describe, it, expect } from '@jest/globals';
import { request } from '../setup';

describe('Layers API', () => {
  it('should get layers list', async () => {
    const response = await request
      .get('/api/v1/layers')
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
    expect(response.body.data).toBeInstanceOf(Array);
  });

  it('should create new layer', async () => {
    const layer = {
      name: 'Test Layer',
      type: 'vector',
      url: 'https://example.com/layer.json'
    };

    const response = await request
      .post('/api/v1/layers')
      .set('Authorization', `Bearer ${token}`)
      .send(layer);

    expect(response.status).toBe(201);
    expect(response.body.data).toHaveProperty('id');
  });
});
```

### 3.3 性能测试

#### 3.3.1 K6配置
```javascript
// k6.config.js
export default {
  scenarios: {
    layers_api: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '30s', target: 50 },
        { duration: '1m', target: 50 },
        { duration: '30s', target: 0 },
      ],
    },
  },
  thresholds: {
    http_req_duration: ['p(95)<500'],
    http_req_failed: ['rate<0.01'],
  },
};
```

#### 3.3.2 性能测试脚本
```javascript
// layers-performance.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export default function() {
  const response = http.get('http://test-api.mtdt.com/api/v1/layers');
  
  check(response, {
    'is status 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });

  sleep(1);
}
```

## 4. 测试用例设计

### 4.1 用例模板

```typescript
interface TestCase {
  id: string;
  title: string;
  description: string;
  preconditions: string[];
  steps: {
    step: string;
    expected: string;
  }[];
  priority: 'high' | 'medium' | 'low';
  type: 'functional' | 'performance' | 'security';
}
```

### 4.2 用例示例

```typescript
// 地图图层加载测试用例
const layerLoadingTest: TestCase = {
  id: 'MAP-001',
  title: '验证地图图层加载功能',
  description: '测试添加不同类型的地图图层并验证加载状态',
  preconditions: [
    '系统已正常启动',
    '用户已登录',
    '已进入地图页面'
  ],
  steps: [
    {
      step: '点击图层控制按钮',
      expected: '显示图层列表面板'
    },
    {
      step: '选择添加矢量图层',
      expected: '显示图层添加对话框'
    },
    {
      step: '输入图层URL并确认',
      expected: '图层成功加载并显示在地图上'
    }
  ],
  priority: 'high',
  type: 'functional'
};
```

## 5. 自动化测试

### 5.1 CI/CD集成

#### 5.1.1 GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Upload coverage
      uses: codecov/codecov-action@v2
```

#### 5.1.2 Jenkins Pipeline
```groovy
// Jenkinsfile
pipeline {
    agent any
    
    stages {
        stage('Build') {
            steps {
                sh 'npm ci'
            }
        }
        
        stage('Test') {
            parallel {
                stage('Unit Tests') {
                    steps {
                        sh 'npm run test:unit'
                    }
                }
                
                stage('E2E Tests') {
                    steps {
                        sh 'npm run test:e2e'
                    }
                }
            }
        }
        
        stage('Report') {
            steps {
                junit '**/junit.xml'
                publishHTML(target: [
                    reportDir: 'coverage',
                    reportFiles: 'index.html',
                    reportName: 'Coverage Report'
                ])
            }
        }
    }
}
```

### 5.2 测试报告

#### 5.2.1 报告配置
```typescript
// jest.config.js
module.exports = {
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'reports/junit',
      outputName: 'junit.xml',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}',
    }],
    ['jest-html-reporter', {
      pageTitle: 'Test Report',
      outputPath: 'reports/test-report.html',
      includeFailureMsg: true,
    }],
  ],
};
```

#### 5.2.2 覆盖率报告
```typescript
// nyc.config.js
module.exports = {
  extends: '@istanbuljs/nyc-config-typescript',
  all: true,
  include: ['src/**/*.ts'],
  exclude: ['src/**/*.spec.ts'],
  reporter: ['text', 'html', 'lcov'],
  reportDir: 'coverage',
};
```

## 6. 测试数据管理

### 6.1 测试数据准备

#### 6.1.1 数据生成器
```typescript
// test/utils/generators.ts
import { faker } from '@faker-js/faker';

export const generateLayer = () => ({
  name: faker.random.words(2),
  type: faker.helpers.arrayElement(['vector', 'raster', 'terrain']),
  url: faker.internet.url(),
  metadata: {
    description: faker.lorem.sentence(),
    source: faker.company.name(),
    updateDate: faker.date.recent().toISOString()
  }
});

export const generateFeature = () => ({
  type: 'Feature',
  geometry: {
    type: 'Point',
    coordinates: [
      faker.address.longitude(),
      faker.address.latitude()
    ]
  },
  properties: {
    name: faker.random.words(2),
    type: faker.helpers.arrayElement(['poi', 'building', 'road']),
    created: faker.date.recent().toISOString()
  }
});
```

#### 6.1.2 数据清理
```typescript
// test/utils/cleanup.ts
export async function cleanupTestData() {
  await Promise.all([
    prisma.layer.deleteMany({
      where: {
        name: {
          startsWith: 'test_'
        }
      }
    }),
    prisma.feature.deleteMany({
      where: {
        properties: {
          path: ['type'],
          equals: 'test'
        }
      }
    })
  ]);
}
```

### 6.2 测试数据库

#### 6.2.1 测试数据库配置
```typescript
// test/config/database.ts
export const testDbConfig = {
  host: process.env.TEST_DB_HOST || 'localhost',
  port: parseInt(process.env.TEST_DB_PORT || '5432'),
  database: process.env.TEST_DB_NAME || 'mtdt_test',
  username: process.env.TEST_DB_USER || 'test_user',
  password: process.env.TEST_DB_PASS || 'test_pass'
};
```

#### 6.2.2 数据库初始化
```typescript
// test/setup/database.ts
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function setupTestDatabase() {
  // 创建测试数据库
  await execAsync(`createdb ${testDbConfig.database}`);
  
  // 运行迁移
  await execAsync('npm run migrate:test');
  
  // 加载测试数据
  await execAsync('npm run seed:test');
}
```

## 7. 测试监控

### 7.1 测试度量

#### 7.1.1 覆盖率目标
```json
{
  "coverage": {
    "statements": 80,
    "branches": 80,
    "functions": 80,
    "lines": 80
  }
}
```

#### 7.1.2 性能指标
```typescript
interface PerformanceMetrics {
  responseTime: {
    p50: number; // 中位数响应时间
    p90: number; // 90%响应时间
    p99: number; // 99%响应时间
  };
  throughput: number; // 每秒请求数
  errorRate: number; // 错误率
  concurrentUsers: number; // 并发用户数
}

const performanceTargets: PerformanceMetrics = {
  responseTime: {
    p50: 200, // 200ms
    p90: 500, // 500ms
    p99: 1000 // 1s
  },
  throughput: 100, // 100 RPS
  errorRate: 0.01, // 1%错误率
  concurrentUsers: 1000 // 1000并发用户
};
```

### 7.2 测试报告分析

#### 7.2.1 趋势分析
```typescript
interface TestTrend {
  date: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  coverage: {
    statements: number;
    branches: number;
    functions: number;
    lines: number;
  };
  performance: PerformanceMetrics;
}
```

#### 7.2.2 问题追踪
```typescript
interface TestIssue {
  id: string;
  type: 'bug' | 'performance' | 'flaky';
  description: string;
  priority: 'high' | 'medium' | 'low';
  status: 'open' | 'investigating' | 'fixed' | 'closed';
  created: string;
  updated: string;
  assignee?: string;
}
```

## 8. 测试流程

### 8.1 开发测试流程

```mermaid
graph TD
    A[需求分析] --> B[测试计划]
    B --> C[用例设计]
    C --> D[用例评审]
    D --> E[测试执行]
    E --> F[缺陷修复]
    F --> G[回归测试]
    G --> H[测试报告]
```

### 8.2 发布测试流程

```mermaid
graph TD
    A[代码提交] --> B[CI构建]
    B --> C[自动化测试]
    C --> D[代码评审]
    D --> E[功能测试]
    E --> F[性能测试]
    F --> G[安全测试]
    G --> H[发布审批]
```

## 9. 总结

本测试规范提供了山思数字平台的完整测试方案，包括：

1. **测试策略**
   - 测试层次划分
   - 测试类型定义
   - 测试环境规划

2. **测试实现**
   - 前端测试方案
   - 后端测试方案
   - 自动化测试集成

3. **测试管理**
   - 用例设计规范
   - 测试数据管理
   - 测试监控分析

4. **测试流程**
   - 开发测试流程
   - 发布测试流程
   - 持续集成流程

建议定期审查和更新本规范，确保测试策略的有效性和适用性。