{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@liaoliaots/nestjs-redis": "^10.0.0", "@nestjs/common": "^10.4.18", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.4.18", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.4.18", "@nestjs/testing": "^10.4.18", "@nestjs/typeorm": "^10.0.2", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.24"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/body-parser": "^1.19.6", "@types/cookie-parser": "^1.4.7", "@types/express": "^5.0.0", "@types/istanbul-lib-report": "^3.0.3", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/passport-strategy": "^0.2.38", "@types/qs": "^6.14.0", "@types/range-parser": "^1.2.7", "@types/send": "^0.17.5", "@types/serve-static": "^1.15.8", "@types/supertest": "^6.0.2", "@types/yargs-parser": "^21.0.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}