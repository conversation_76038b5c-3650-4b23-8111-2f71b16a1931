{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.13433837890625, "root": {"boundingVolume": {"box": [7896.0693359375, 883.1988525390625, -928.330810546875, 108.443603515625, 0.0, 0.0, 0.0, 138.28286743164062, 0.0, 0.0, 0.0, 121.69149780273438]}, "children": [{"boundingVolume": {"box": [7896.0693359375, 814.0574340820312, -967.2835693359375, 108.443603515625, 0.0, 0.0, 0.0, 69.14141845703125, 0.0, 0.0, 0.0, 82.73876953125]}, "children": [{"boundingVolume": {"box": [7841.84765625, 814.0574340820312, -949.0941162109375, 54.2216796875, 0.0, 0.0, 0.0, 69.14141845703125, 0.0, 0.0, 0.0, 64.54934692382812]}, "children": [{"boundingVolume": {"box": [7814.73681640625, 779.4866943359375, -968.4403686523438, 27.11083984375, 0.0, 0.0, 0.0, 34.570709228515625, 0.0, 0.0, 0.0, 45.203125]}, "content": {"uri": "BlockA_L19_699.b3dm"}, "geometricError": 0.13634037971496582, "refine": "REPLACE"}, {"boundingVolume": {"box": [7868.95849609375, 779.4866943359375, -945.0617065429688, 27.11083984375, 0.0, 0.0, 0.0, 34.570709228515625, 0.0, 0.0, 0.0, 31.952392578125]}, "content": {"uri": "BlockA_L19_698.b3dm"}, "geometricError": 0.12817229330539703, "refine": "REPLACE"}, {"boundingVolume": {"box": [7841.84765625, 848.628173828125, -916.187744140625, 54.2216796875, 0.0, 0.0, 0.0, 34.570709228515625, 0.0, 0.0, 0.0, 31.332489013671875]}, "content": {"uri": "BlockA_L19_697.b3dm"}, "geometricError": 0.12377596646547318, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2187.b3dm"}, "geometricError": 0.25640642642974854, "refine": "REPLACE"}, {"boundingVolume": {"box": [7950.291015625, 814.0574340820312, -974.258056640625, 54.221923828125, 0.0, 0.0, 0.0, 69.14141845703125, 0.0, 0.0, 0.0, 70.86874389648438]}, "children": [{"boundingVolume": {"box": [7971.5439453125, 814.0574340820312, -1009.3118896484375, 32.969482421875, 0.0, 0.0, 0.0, 69.14141845703125, 0.0, 0.0, 0.0, 35.2451171875]}, "content": {"uri": "BlockA_L19_696.b3dm"}, "geometricError": 0.14728178083896637, "refine": "REPLACE"}, {"boundingVolume": {"box": [7931.4736328125, 814.0574340820312, -938.8216552734375, 35.404052734375, 0.0, 0.0, 0.0, 69.14141845703125, 0.0, 0.0, 0.0, 35.245147705078125]}, "content": {"uri": "BlockA_L19_695.b3dm"}, "geometricError": 0.1321212500333786, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2186.b3dm"}, "geometricError": 0.27735185623168945, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1126.b3dm"}, "geometricError": 0.5316170454025269, "refine": "REPLACE"}, {"boundingVolume": {"box": [7896.0693359375, 952.34033203125, -921.3832397460938, 108.443603515625, 0.0, 0.0, 0.0, 69.14144897460938, 0.0, 0.0, 0.0, 114.71478271484375]}, "children": [{"boundingVolume": {"box": [7896.0693359375, 952.34033203125, -966.5316162109375, 108.443603515625, 0.0, 0.0, 0.0, 69.14144897460938, 0.0, 0.0, 0.0, 64.05783081054688]}, "children": [{"boundingVolume": {"box": [7850.884765625, 916.921875, -914.6103515625, 63.2587890625, 0.0, 0.0, 0.0, 33.723052978515625, 0.0, 0.0, 0.0, 12.136505126953125]}, "content": {"uri": "BlockA_L19_694.b3dm"}, "geometricError": 0.12191715836524963, "refine": "REPLACE"}, {"boundingVolume": {"box": [7959.328125, 952.34033203125, -966.5316162109375, 45.184814453125, 0.0, 0.0, 0.0, 69.14144897460938, 0.0, 0.0, 0.0, 64.05783081054688]}, "content": {"uri": "BlockA_L19_693.b3dm"}, "geometricError": 0.1436803787946701, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2185.b3dm"}, "geometricError": 0.2802819609642029, "refine": "REPLACE"}, {"boundingVolume": {"box": [7881.6044921875, 952.34033203125, -855.3011474609375, 93.978759765625, 0.0, 0.0, 0.0, 69.14144897460938, 0.0, 0.0, 0.0, 47.172698974609375]}, "children": [{"boundingVolume": {"box": [7826.5810546875, 952.34033203125, -856.7182006835938, 38.955078125, 0.0, 0.0, 0.0, 69.14144897460938, 0.0, 0.0, 0.0, 45.755615234375]}, "content": {"uri": "BlockA_L19_692.b3dm"}, "geometricError": 0.15427622199058533, "refine": "REPLACE"}, {"boundingVolume": {"box": [7920.0732421875, 952.34033203125, -868.932861328125, 54.537109375, 0.0, 0.0, 0.0, 69.14144897460938, 0.0, 0.0, 0.0, 33.540985107421875]}, "content": {"uri": "BlockA_L19_691.b3dm"}, "geometricError": 0.17169000208377838, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2184.b3dm"}, "geometricError": 0.3276582658290863, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1125.b3dm"}, "geometricError": 0.612930178642273, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_566.b3dm"}, "geometricError": 1.13433837890625, "refine": "REPLACE"}}