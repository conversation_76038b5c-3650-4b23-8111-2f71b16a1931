import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { Role } from './role.entity';
import { UserSession } from './user-session.entity';
import { AuditLog } from './audit-log.entity';

@Entity('users', { schema: 'system' })
export class User {
  @PrimaryGeneratedColumn('uuid')
  user_id: string;

  @Column({ length: 50, unique: true })
  username: string;

  @Column({ length: 255, unique: true })
  email: string;

  @Column({ length: 255 })
  password_hash: string;

  @Column({ length: 100, nullable: true })
  full_name: string;

  @Column({ length: 100, nullable: true })
  department: string;

  @Column({ length: 20, nullable: true })
  phone: string;

  @Column({ default: true })
  is_active: boolean;

  @Column({ default: true })
  is_first_login: boolean;

  @Column({ type: 'timestamptz', nullable: true })
  last_login: Date;

  @Column({ default: 0 })
  login_attempts: number;

  @Column({ type: 'timestamptz', nullable: true })
  locked_until: Date | null;

  @Column({ type: 'timestamptz', nullable: true })
  password_changed_at: Date | null;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by: string;

  // 关联关系
  @ManyToMany(() => Role, role => role.users)
  @JoinTable({
    name: 'user_roles',
    schema: 'system',
    joinColumn: { name: 'user_id', referencedColumnName: 'user_id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'role_id' },
  })
  roles: Role[];

  @OneToMany(() => UserSession, session => session.user)
  sessions: UserSession[];

  @OneToMany(() => AuditLog, log => log.user)
  audit_logs: AuditLog[];

  // 虚拟字段 - 密码（用于接收明文密码）
  password?: string;

  // 驼峰命名的访问器
  get id(): string {
    return this.user_id;
  }

  get fullName(): string {
    return this.full_name || '';
  }

  set fullName(value: string) {
    this.full_name = value;
  }

  get isActive(): boolean {
    return this.is_active;
  }

  set isActive(value: boolean) {
    this.is_active = value;
  }

  get isFirstLogin(): boolean {
    return this.is_first_login;
  }

  set isFirstLogin(value: boolean) {
    this.is_first_login = value;
  }

  get lastLoginAt(): Date {
    return this.last_login;
  }

  set lastLoginAt(value: Date) {
    this.last_login = value;
  }

  get failedLoginAttempts(): number {
    return this.login_attempts;
  }

  set failedLoginAttempts(value: number) {
    this.login_attempts = value;
  }

  get lockedAt(): Date | null {
    return this.locked_until;
  }

  set lockedAt(value: Date | null) {
    this.locked_until = value;
  }

  get lockExpiresAt(): Date | null {
    return this.locked_until;
  }

  set lockExpiresAt(value: Date | null) {
    this.locked_until = value;
  }

  get passwordHash(): string {
    return this.password_hash;
  }

  set passwordHash(value: string) {
    this.password_hash = value;
  }

  get passwordChangedAt(): Date | null {
    return this.password_changed_at;
  }

  set passwordChangedAt(value: Date | null) {
    this.password_changed_at = value;
  }

  get createdAt(): Date {
    return this.created_at;
  }

  get updatedAt(): Date {
    return this.updated_at;
  }

  get isLocked(): boolean {
    return !!(this.locked_until && new Date() < this.locked_until);
  }

  // 钩子方法
  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password) {
      const rounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
      this.password_hash = await bcrypt.hash(this.password, rounds);
      delete this.password; // 删除明文密码
    }
  }

  // 实例方法
  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password_hash);
  }

  async incrementLoginAttempts(): Promise<void> {
    this.login_attempts += 1;
    
    const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5');
    if (this.login_attempts >= maxAttempts) {
      const lockTimeMinutes = parseInt(process.env.LOCK_TIME?.replace('m', '') || '15');
      this.locked_until = new Date(Date.now() + lockTimeMinutes * 60 * 1000);
    }
  }

  resetLoginAttempts(): void {
    this.login_attempts = 0;
    this.locked_until = null;
  }

  updateLastLogin(): void {
    this.last_login = new Date();
    this.resetLoginAttempts();
  }

  // 获取用户权限
  getPermissions(): string[] {
    if (!this.roles) return [];
    
    const permissions = new Set<string>();
    
    for (const role of this.roles) {
      const rolePermissions = role.getPermissions();
      rolePermissions.forEach(permission => permissions.add(permission));
    }
    
    return Array.from(permissions);
  }

  // 检查权限
  hasPermission(permission: string): boolean {
    const userPermissions = this.getPermissions();
    
    // 检查是否有超级权限
    if (userPermissions.includes('*')) {
      return true;
    }
    
    // 检查具体权限
    return userPermissions.includes(permission);
  }

  // 检查角色
  hasRole(roleName: string): boolean {
    if (!this.roles) return false;
    return this.roles.some(role => role.name === roleName);
  }

  // 转换为安全的输出格式（排除敏感信息）
  toSafeObject() {
    const { password_hash, password, login_attempts, locked_until, ...safeUser } = this;
    return {
      ...safeUser,
      id: this.id,
      fullName: this.fullName,
      isActive: this.isActive,
      isFirstLogin: this.isFirstLogin,
      lastLoginAt: this.lastLoginAt,
      failedLoginAttempts: this.failedLoginAttempts,
      isLocked: this.isLocked,
      passwordChangedAt: this.passwordChangedAt,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      roles: this.roles?.map(role => ({
        id: role.role_id,
        name: role.name,
        displayName: role.display_name,
        description: role.description,
      })),
      permissions: this.getPermissions(),
    };
  }
}