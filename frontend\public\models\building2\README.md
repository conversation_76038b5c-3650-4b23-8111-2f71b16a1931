# 示例3D Tiles文件

这是一个示例的tileset.json文件，用于演示目录结构。

**重要提示**: 这只是一个占位文件，实际使用时请替换为您真实的3D Tiles数据。

## 文件说明

- `tileset.json`: 3D Tiles的入口文件，包含瓦片集的元数据
- `*.b3dm`: 批量3D模型瓦片文件
- `*.i3dm`: 实例化3D模型瓦片文件  
- `*.pnts`: 点云瓦片文件
- `*.cmpt`: 复合瓦片文件

## 使用方法

1. 将您的真实3D Tiles数据放在此目录中
2. 确保有一个有效的 `tileset.json` 文件
3. 在界面中使用路径：`/models/building1/tileset.json`

## 示例tileset.json结构

```json
{
  "asset": {
    "version": "1.0"
  },
  "geometricError": 500,
  "root": {
    "boundingVolume": {
      "region": [
        1.2, 0.6, 1.21, 0.61, 0, 100
      ]
    },
    "geometricError": 100,
    "refine": "REPLACE",
    "content": {
      "uri": "0.b3dm"
    }
  }
}
```

---
*请将此文件替换为您的实际3D Tiles数据*
