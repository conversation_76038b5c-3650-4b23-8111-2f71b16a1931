import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsE<PERSON>, Max<PERSON>ength } from 'class-validator';

export class LoginDto {
  @IsString()
  @IsNotEmpty()
  username: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6, { message: '密码长度至少6位' })
  password: string;

  @IsOptional()
  @IsString()
  captcha?: string;
}

export class LoginResponseDto {
  access_token: string;
  refresh_token: string;
  user: {
    user_id: string;
    username: string;
    email: string;
    full_name: string;
    roles: string[];
    permissions: string[];
  };
  expires_in: number;
}

export class RefreshTokenDto {
  @IsOptional()
  @IsString()
  refreshToken?: string;
}

export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty()
  currentPassword: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8, { message: '新密码长度至少8位' })
  newPassword: string;
}

// 邮箱验证码相关DTO
export class SendEmailCodeDto {
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  email: string;

  @IsOptional()
  @IsString()
  type?: 'login' | 'register' | 'reset'; // 验证码类型
}

export class EmailLoginDto {
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  email: string;

  @IsString()
  @IsNotEmpty({ message: '验证码不能为空' })
  @MinLength(6, { message: '验证码长度为6位' })
  code: string;
}

export class FirstLoginDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(8, { message: '密码长度至少8位' })
  password: string;

  @IsOptional()
  @IsString()
  full_name?: string;

  @IsOptional()
  @IsString()
  phone?: string;
}

export class RegisterDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: '用户名长度至少3位' })
  username: string;

  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6, { message: '验证码长度为6位' })
  @MaxLength(6, { message: '验证码长度为6位' })
  emailCode: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8, { message: '密码长度至少8位' })
  password: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8, { message: '确认密码长度至少8位' })
  confirmPassword: string;

  @IsOptional()
  @IsString()
  fullName?: string;

  @IsOptional()
  @IsString()
  phone?: string;
}

export class RegisterResponseDto {
  message: string;
  user: {
    id: string;
    username: string;
    email: string;
    fullName: string;
  };
}