{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.3059245347976685, "root": {"boundingVolume": {"box": [9834.90234375, 3771.923828125, -524.8851928710938, 149.599609375, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 121.9530029296875]}, "children": [{"boundingVolume": {"box": [9760.1025390625, 3771.923828125, -567.0265502929688, 74.7998046875, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 77.17805480957031]}, "children": [{"boundingVolume": {"box": [9760.1025390625, 3722.275390625, -566.7381591796875, 74.7998046875, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 74.78315734863281]}, "children": [{"boundingVolume": {"box": [9722.703125, 3722.275390625, -590.9702758789062, 37.39990234375, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 50.551025390625]}, "content": {"uri": "BlockA_L18_12.b3dm"}, "geometricError": 0.1823008954524994, "refine": "REPLACE"}, {"boundingVolume": {"box": [9797.501953125, 3722.275390625, -550.869140625, 37.39990234375, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 58.87408447265625]}, "content": {"uri": "BlockA_L19_205.b3dm"}, "geometricError": 0.17128075659275055, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1943.b3dm"}, "geometricError": 0.3524809777736664, "refine": "REPLACE"}, {"boundingVolume": {"box": [9760.1025390625, 3821.572265625, -552.7347412109375, 74.7998046875, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 60.557708740234375]}, "children": [{"boundingVolume": {"box": [9722.703125, 3821.572265625, -553.3685302734375, 37.39990234375, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 59.8712158203125]}, "content": {"uri": "BlockA_L18_11.b3dm"}, "geometricError": 0.17712891101837158, "refine": "REPLACE"}, {"boundingVolume": {"box": [9797.501953125, 3821.572265625, -534.9445190429688, 37.39990234375, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 42.76751708984375]}, "content": {"uri": "BlockA_L19_204.b3dm"}, "geometricError": 0.17093124985694885, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1942.b3dm"}, "geometricError": 0.3473998010158539, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1008.b3dm"}, "geometricError": 0.6997366547584534, "refine": "REPLACE"}, {"boundingVolume": {"box": [9909.7021484375, 3771.923828125, -491.4692687988281, 74.7998046875, 0.0, 0.0, 0.0, 99.296875, 0.0, 0.0, 0.0, 88.53707885742188]}, "children": [{"boundingVolume": {"box": [9909.7021484375, 3722.275390625, -496.13372802734375, 74.7998046875, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 83.59318542480469]}, "children": [{"boundingVolume": {"box": [9909.7021484375, 3722.275390625, -531.0006103515625, 74.7998046875, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 48.72633361816406]}, "content": {"uri": "BlockA_L19_203.b3dm"}, "geometricError": 0.16381359100341797, "refine": "REPLACE"}, {"boundingVolume": {"box": [9921.826171875, 3728.12158203125, -447.4697265625, 62.67626953125, 0.0, 0.0, 0.0, 43.8023681640625, 0.0, 0.0, 0.0, 34.80451965332031]}, "content": {"uri": "BlockA_L19_202.b3dm"}, "geometricError": 0.14828525483608246, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1941.b3dm"}, "geometricError": 0.31103283166885376, "refine": "REPLACE"}, {"boundingVolume": {"box": [9909.7021484375, 3821.572265625, -471.3949890136719, 74.7998046875, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 68.46279907226562]}, "children": [{"boundingVolume": {"box": [9872.302734375, 3821.572265625, -494.5484313964844, 37.39990234375, 0.0, 0.0, 0.0, 49.6484375, 0.0, 0.0, 0.0, 45.309356689453125]}, "content": {"uri": "BlockA_L19_201.b3dm"}, "geometricError": 0.1596032679080963, "refine": "REPLACE"}, {"boundingVolume": {"box": [9947.1015625, 3796.748046875, -443.388916015625, 37.39990234375, 0.0, 0.0, 0.0, 24.82421875, 0.0, 0.0, 0.0, 40.14143371582031]}, "content": {"uri": "BlockA_L19_200.b3dm"}, "geometricError": 0.14388535916805267, "refine": "REPLACE"}, {"boundingVolume": {"box": [9947.1015625, 3846.396484375, -459.07427978515625, 37.39990234375, 0.0, 0.0, 0.0, 24.82421875, 0.0, 0.0, 0.0, 35.191375732421875]}, "content": {"uri": "BlockA_L19_199.b3dm"}, "geometricError": 0.15052375197410583, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1940.b3dm"}, "geometricError": 0.30529919266700745, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1007.b3dm"}, "geometricError": 0.6162561178207397, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_506.b3dm"}, "geometricError": 1.3059245347976685, "refine": "REPLACE"}}