<template>
  <div ref="cesiumContainer" class="cesium-container">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
      <span>正在加载3D地球...</span>
    </div>


    <!-- ESC提示 -->
    <div v-if="showEscTip" class="esc-tip">
      <el-alert
        title="按 ESC 键退出当前工具"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-overlay">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
      <el-button @click="retryInit" type="primary" size="small" style="margin-top: 10px;">
        重试
      </el-button>
    </div>

    <!-- 坐标显示组件 -->
    <CoordinateDisplay ref="coordinateDisplay" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as Cesium from 'cesium'
// 修改为实际的map store路径，假设为src/stores/map.ts
import { useMapStore } from '../../stores/map'
import { ElMessage } from 'element-plus'
// 修改为实际的测量工具文件路径，例如如果在 utils 目录下为 measure-tools.ts 则如下：
import { MeasureTools, type MeasureResult } from '../../utils/measureTools'
// 如果实际路径不同，请将 '@/utils/measure-tools' 替换为正确的相对路径，如 '../../utils/measureTools'
import { DrawTools, type DrawResult } from '../../utils/drawTools'
import { CoordinateTracker, type CoordinateInfo, type ScreenPosition } from '../../utils/coordinateTracker'
import { createMapQualityOptimizer, type MapQualityOptimizer } from '../../utils/mapQualityOptimizer'
import CoordinateDisplay from './CoordinateDisplay.vue'
import type { PropType } from 'vue'

const props = defineProps({
  activeTool: {
    type: String,
    default: ''
  },
  imageryProviderViewModels: {
    type: Array as PropType<Cesium.ProviderViewModel[]>,
    default: () => []
  }
})

const emit = defineEmits<{
  areaSelect: [area: any]
  measureResult: [result: MeasureResult]
  measureStart: [type: string]
  measureStop: []
  drawResult: [result: DrawResult]
  drawStart: [type: string]
  drawStop: []
}>()

const cesiumContainer = ref<HTMLDivElement>()
const loading = ref(true)
const error = ref('')
//const statusText = ref('初始化中...')
const mapStore = useMapStore()
//const statusType = ref('info')
const showEscTip = ref(false)
const coordinateDisplay = ref<InstanceType<typeof CoordinateDisplay>>()

let viewer: Cesium.Viewer | null = null
let measureTools: MeasureTools | null = null
let drawTools: DrawTools | null = null
let coordinateTracker: CoordinateTracker | null = null
let qualityOptimizer: MapQualityOptimizer | null = null

onMounted(async () => {
  console.log('CesiumViewer组件已挂载')
  await nextTick()
  await initCesium()
  
  // 添加ESC键监听
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
  if (coordinateTracker) {
    coordinateTracker.destroy()
    coordinateTracker = null
  }
  if (qualityOptimizer) {
    qualityOptimizer = null
  }
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// ESC键处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    if (measureTools && props.activeTool.startsWith('measure-')) {
      measureTools.stopMeasure()
      emit('measureStop')
    }
    if (drawTools && props.activeTool.startsWith('draw-')) {
      drawTools.stopDraw()
      emit('drawStop')
    }
  }
}

watch(() => props.activeTool, (newTool, oldTool) => {
  handleToolChange(newTool, oldTool)
  showEscTip.value = [
    'measure-distance', 'measure-area', 'measure-height', 
    'draw-point', 'draw-line', 'draw-polygon', 'draw-circle', 'draw-rectangle'
  ].includes(newTool)
})

// watch([error, loading, statusText], () => {
//   if (error.value) {
//     statusType.value = 'danger'
//   } else if (loading.value) {
//     statusType.value = 'info'
//   } else if (statusText.value.includes('离线')) {
//     statusType.value = 'warning'
//   } else {
//     statusType.value = 'success'
//   }
// }, { immediate: true })

const retryInit = async () => {
  error.value = ''
  loading.value = true
  await initCesium()
}

const initCesium = async () => {
  if (!cesiumContainer.value) {
    error.value = 'Cesium容器未找到'
    loading.value = false
    return
  }

  try {
    console.log('开始初始化Cesium...')
    
    // 首先尝试在线模式
    await initOnlineMode()
    
  } catch (onlineError) {
    console.log('在线模式失败，尝试离线模式:', onlineError)
    
    try {
      await initOfflineMode()
    } catch (offlineError) {
      console.error('离线模式也失败:', offlineError)
      error.value = '地图初始化失败，请检查网络连接'
      loading.value = false
      return
    }
  }

  // 设置初始视角
  await setInitialView()
  
  // 加载数据
  await loadMapData()
  
  // 设置交互
  setupInteractions()
  
  // 初始化测量工具
  initMeasureTools()

  // 初始化绘图工具
  initDrawTools()

  // 初始化坐标跟踪器
  initCoordinateTracker()

  // 自定义Home按钮行为，让它定位到中国
  if (viewer && viewer.homeButton) {
    viewer.homeButton.viewModel.command.beforeExecute.addEventListener((e: any) => {
      e.cancel = true // 取消默认行为
      // 调用我们自定义的goHome函数
      mapStore.goHome()
    })
  }

  // 存储到store
  if (viewer) {
    mapStore.setViewer(viewer)
  }
  
  loading.value = false
  console.log('Cesium初始化完成')
}

const initOnlineMode = async () => {
  // 设置Cesium Ion访问令牌
  Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************._Wk3WsAUjBsz50AW1CfNYW9tsEbkJYIBLpVxZSFhZa8'

  viewer = new Cesium.Viewer(cesiumContainer.value!, {
    // 使用Cesium世界地形提供真实的3D地形
    terrainProvider: await Cesium.createWorldTerrainAsync({
      requestWaterMask: true, // 请求水体遮罩
      requestVertexNormals: true // 请求顶点法线以改善光照效果
    }),
    animation: false,
    timeline: false,
    fullscreenButton: false,
    vrButton: false,
    homeButton: true,
    sceneModePicker: true,
    baseLayerPicker: false,
    navigationHelpButton: false,
    navigationInstructionsInitiallyVisible: false,
    imageryProviderViewModels: props.imageryProviderViewModels as Cesium.ProviderViewModel[],
    creditContainer: document.createElement('div'), // 隐藏版权信息
    // 性能优化设置
    requestRenderMode: true, // 按需渲染
    maximumRenderTimeChange: Infinity, // 减少不必要的渲染
    contextOptions: {
      webgl: {
        alpha: false, // 禁用透明度，提升性能
        antialias: true, // 启用抗锯齿
        preserveDrawingBuffer: false,
        failIfMajorPerformanceCaveat: false
      }
    }
  })

  // 启用深度测试以改善地形显示
  viewer.scene.globe.depthTestAgainstTerrain = true

  // 初始化质量优化器并应用最佳设置
  qualityOptimizer = createMapQualityOptimizer(viewer)
  qualityOptimizer.applyOptimalSettings()

  // 可选：加载建筑（可能影响性能）
  // try {
  //   const buildingTileset = await Cesium.createOsmBuildingsAsync()
  //   viewer.scene.primitives.add(buildingTileset)
  // } catch (buildingError) {
  //   console.warn('建筑加载失败:', buildingError)
  // }
}

const initOfflineMode = async () => {
  // 创建高性能的影像提供者
  const osmProvider = new Cesium.OpenStreetMapImageryProvider({
    url: 'https://a.tile.openstreetmap.org/',
    maximumLevel: 18 // 限制最大缩放级别，提升性能
  })

  viewer = new Cesium.Viewer(cesiumContainer.value!, {
    // 离线模式也尝试使用世界地形，如果失败则降级到椭球地形
    terrainProvider: await Cesium.createWorldTerrainAsync({
      requestWaterMask: true,
      requestVertexNormals: true
    }).catch(() => {
      console.warn('世界地形加载失败，使用椭球地形')
      return new Cesium.EllipsoidTerrainProvider()
    }),
    baseLayerPicker: false,
    geocoder: false,
    animation: false,
    timeline: false,
    fullscreenButton: false,
    vrButton: false,
    homeButton: true,
    sceneModePicker: true,
    navigationHelpButton: false,
    creditContainer: document.createElement('div'), // 隐藏版权信息
    // 性能优化设置
    requestRenderMode: true,
    maximumRenderTimeChange: Infinity,
    contextOptions: {
      webgl: {
        alpha: false,
        antialias: true,
        preserveDrawingBuffer: false,
        failIfMajorPerformanceCaveat: false
      }
    }
  })

  // 设置影像图层
  viewer.imageryLayers.removeAll()
  const imageryLayer = viewer.imageryLayers.add(new Cesium.ImageryLayer(osmProvider))

  // 优化影像图层设置
  imageryLayer.brightness = 1.0
  imageryLayer.contrast = 1.0
  imageryLayer.gamma = 1.0

  // 启用深度测试以改善地形显示
  viewer.scene.globe.depthTestAgainstTerrain = true

  // 初始化质量优化器并应用最佳设置
  qualityOptimizer = createMapQualityOptimizer(viewer)
  qualityOptimizer.applyOptimalSettings()
}



const setInitialView = async () => {
  if (!viewer) return

  // 设置初始视角到中国西南地区，使用适合观看3D地形的角度
  await viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(104.1, 32.6, 2000000),
    orientation: {
      heading: Cesium.Math.toRadians(0.0),
      pitch: Cesium.Math.toRadians(-45.0), // 45度俯视角，更好地展示地形起伏
      roll: Cesium.Math.toRadians(0.0)
    },
    duration: 2.0
  })
}

const loadMapData = async () => {
  if (!viewer) return

  console.log('加载保护区数据...')

  console.log('地图数据加载完成')
}

const setupInteractions = () => {
  if (!viewer) return

  // 点击事件
  viewer.cesiumWidget.screenSpaceEventHandler.setInputAction((event: any) => {
    // 如果正在测量或绘图，不处理区域选择
    if (props.activeTool.startsWith('measure-') || props.activeTool.startsWith('draw-')) {
      return
    }

    const pickedObject = viewer!.scene.pick(event.position)
    
    if (Cesium.defined(pickedObject) && pickedObject.id) {
      const entity = pickedObject.id
      if (entity.properties && entity.properties.areaData) {
        emit('areaSelect', entity.properties.areaData.getValue())
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

  // 鼠标悬停效果
  viewer.cesiumWidget.screenSpaceEventHandler.setInputAction((event: any) => {
    if (props.activeTool.startsWith('measure-') || props.activeTool.startsWith('draw-')) {
      return
    }

    const pickedObject = viewer!.scene.pick(event.endPosition)
    
    if (Cesium.defined(pickedObject) && pickedObject.id) {
      document.body.style.cursor = 'pointer'
    } else {
      document.body.style.cursor = 'default'
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
}

// 初始化测量工具
const initMeasureTools = () => {
  if (!viewer) return
  
  // 传入回调函数来处理测量完成事件
  measureTools = new MeasureTools(viewer, (result: MeasureResult) => {
    console.log('测量完成，发送事件:', result)
    // 确保事件名称与MainLayout.vue中的事件监听器一致
    emit('measureResult', result)
  })
  
  console.log('测量工具初始化完成')
}

// 初始化绘图工具
const initDrawTools = () => {
  if (!viewer) return

  drawTools = new DrawTools(viewer)
  drawTools.setEventCallbacks({
    onDrawStart: (type: string) => {
      emit('drawStart', type)
    },
    onDrawStop: () => {
      emit('drawStop')
    },
    onDrawResult: (result: DrawResult) => {
      emit('drawResult', result)
    }
  })

  console.log('绘图工具初始化完成')
}

// 初始化坐标跟踪器
const initCoordinateTracker = () => {
  if (!viewer || !coordinateDisplay.value) return

  coordinateTracker = new CoordinateTracker(viewer)

  // 启用坐标跟踪
  coordinateTracker.enable(
    (coordinates: CoordinateInfo, screenPosition: ScreenPosition) => {
      // 更新坐标显示组件（不再需要传递屏幕位置）
      coordinateDisplay.value?.updateCoordinates({
        longitude: coordinates.longitude,
        latitude: coordinates.latitude,
        height: coordinates.height
      })
    },
    () => {
      // 鼠标离开时隐藏坐标显示
      coordinateDisplay.value?.hide()
    }
  )

  console.log('坐标跟踪器初始化完成')
}

const handleToolChange = (newTool: string, oldTool: string) => {
  if (!viewer || !measureTools || !drawTools) return

  console.log('工具切换:', { from: oldTool, to: newTool })

  // 停止之前的测量和绘制
  if (oldTool && oldTool.startsWith('measure-')) {
    measureTools.stopMeasure()
  }
  if (oldTool && oldTool.startsWith('draw-')) {
    drawTools.stopDraw()
  }

  switch (newTool) {
    case 'measure-distance':
      measureTools.startMeasure('distance')
      emit('measureStart', 'distance')
      ElMessage.info('距离测量已启用，单击地图开始测量')
      break
    case 'measure-area':
      measureTools.startMeasure('area')
      emit('measureStart', 'area')
      ElMessage.info('面积测量已启用，单击地图开始测量')
      break
    case 'measure-height':
      measureTools.startMeasure('height')
      emit('measureStart', 'height')
      ElMessage.info('高程测量已启用，单击地图获取高程')
      break
    case 'draw-point':
      drawTools.startDraw('point')
      emit('drawStart', 'point')
      ElMessage.info('点绘制已启用，单击地图添加点')
      break
    case 'draw-line':
      drawTools.startDraw('line')
      emit('drawStart', 'line')
      ElMessage.info('线条绘制已启用，单击地图开始绘制')
      break
    case 'draw-polygon':
      drawTools.startDraw('polygon')
      emit('drawStart', 'polygon')
      ElMessage.info('多边形绘制已启用，单击地图开始绘制')
      break
    case 'draw-circle':
      drawTools.startDraw('circle')
      emit('drawStart', 'circle')
      ElMessage.info('圆形绘制已启用，单击地图开始绘制')
      break
    case 'draw-rectangle':
      drawTools.startDraw('rectangle')
      emit('drawStart', 'rectangle')
      ElMessage.info('矩形绘制已启用，单击地图开始绘  ')
      break
    case 'draw':
      // 确保这里不会启动任何绘制，只是显示面板
      ElMessage.info('绘图工具已启用，请选择具体的绘制类型')
      break
    case 'clear':
      clearAllMarkers()
      break
    default:
      break
  }
}

const clearAllMarkers = () => {
  if (measureTools) {
    measureTools.clearAllMeasures()
  }
  if (drawTools) {
    drawTools.clearAllDrawings()
  }
  ElMessage.success('已清除所有标记、测量和绘制结果')
}

// 暴露给父组件的方法
defineExpose({
  getViewer: () => viewer,
  getMeasureTools: () => measureTools,
  getDrawTools: () => drawTools,
  getCoordinateTracker: () => coordinateTracker,
  getQualityOptimizer: () => qualityOptimizer,
  enableCoordinateDisplay: () => {
    if (coordinateTracker && !coordinateTracker.enabled) {
      initCoordinateTracker()
      ElMessage.success('坐标显示已启用')
    }
  },
  disableCoordinateDisplay: () => {
    if (coordinateTracker && coordinateTracker.enabled) {
      coordinateTracker.disable()
      coordinateDisplay.value?.hide()
      ElMessage.info('坐标显示已禁用')
    }
  },
  zoomToArea: (areaId: string) => {
    if (viewer) {
      const entity = viewer.entities.getById(areaId)
      if (entity) {
        viewer.zoomTo(entity)
      }
    }
  },
  zoomToMeasureResult: (result: MeasureResult) => {
    if (viewer && result.points.length > 0) {
      const positions = result.points.map(p => p.position)
      viewer.zoomTo(viewer.entities.add({
        position: positions[0],
        point: { pixelSize: 1, color: Cesium.Color.TRANSPARENT }
      }))
    }
  },
  zoomToDrawResult: (result: DrawResult) => {
    if (viewer && result.points.length > 0) {
      const positions = result.points.map(point =>
        Cesium.Cartesian3.fromDegrees(point.longitude, point.latitude)
      )

      if (positions.length === 1) {
        // 单点定位
        viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            result.points[0].longitude,
            result.points[0].latitude,
            5000
          )
        })
      } else {
        // 多点定位到边界
        const boundingSphere = Cesium.BoundingSphere.fromPoints(positions)
        viewer.camera.flyToBoundingSphere(boundingSphere, {
          duration: 1.5,
          offset: new Cesium.HeadingPitchRange(0, -0.5, boundingSphere.radius * 2)
        })
      }
    }
  }
})
</script>

<style scoped>
.cesium-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  gap: 16px;
  font-size: 16px;
  color: white;
}

.status-info {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1001;
}

.esc-tip {
  position: absolute;
  top: 50px;
  right: 10px;
  z-index: 1001;
  max-width: 300px;
}

.error-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1002;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

:deep(.cesium-widget) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.cesium-widget canvas) {
  width: 100% !important;
  height: 100% !important;
}
</style>
