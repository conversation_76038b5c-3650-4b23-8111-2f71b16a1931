/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.129
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as e}from"./chunk-G6ISHM24.js";import"./chunk-HNJRTQHB.js";import"./chunk-PJSC3CZC.js";import"./chunk-I4PVU2XE.js";import"./chunk-TMRGWDA2.js";import"./chunk-OST65WKL.js";import"./chunk-KLPRJ6SC.js";import"./chunk-4PT23TTH.js";import"./chunk-J4RA3VLE.js";import"./chunk-I4JBCTLR.js";import"./chunk-MCEXFPZL.js";import"./chunk-5IUKPU5Q.js";import"./chunk-B3NSNNUV.js";import"./chunk-BOXFFUY5.js";import{e as o}from"./chunk-OVZZEY7C.js";function i(r,t){return o(t)&&(r=e.unpack(r,t)),e.createGeometry(r)}var a=i;export{a as default};
