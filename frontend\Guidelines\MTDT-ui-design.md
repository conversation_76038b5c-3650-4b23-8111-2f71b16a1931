# 山思数字平台 UI 设计规范

## 1. 设计系统

### 1.1 色彩系统

```scss
// 主色调
$primary-color: #2B5AED;      // 主要品牌色
$primary-light: #4B7BFF;      // 主色调亮色
$primary-dark: #1A3FA8;       // 主色调暗色

// 功能色
$success-color: #52C41A;      // 成功
$warning-color: #FAAD14;      // 警告
$error-color: #F5222D;        // 错误
$info-color: #1890FF;         // 信息

// 中性色
$text-primary: #262626;       // 主要文字
$text-secondary: #595959;     // 次要文字
$text-disabled: #BFBFBF;      // 禁用文字
$border-color: #D9D9D9;       // 边框颜色
$background: #F5F5F5;         // 背景色
```

### 1.2 字体规范

```scss
// 字体家族
$font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 
              'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;

// 字号
$font-size-xs: 12px;      // 辅助文字
$font-size-sm: 14px;      // 正文（小）
$font-size-base: 16px;    // 正文
$font-size-lg: 20px;      // 标题
$font-size-xl: 24px;      // 大标题
$font-size-xxl: 30px;     // 特大标题

// 行高
$line-height-tight: 1.2;
$line-height-base: 1.5;
$line-height-loose: 1.8;
```

### 1.3 间距规范

```scss
// 间距单位
$spacing-unit: 4px;

// 间距变量
$spacing-xs: $spacing-unit;      // 4px
$spacing-sm: $spacing-unit * 2;  // 8px
$spacing-md: $spacing-unit * 4;  // 16px
$spacing-lg: $spacing-unit * 6;  // 24px
$spacing-xl: $spacing-unit * 8;  // 32px
```

### 1.4 阴影规范

```scss
// 阴影层级
$shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
$shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
$shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.15);
```

## 2. 组件规范

### 2.1 按钮规范

```scss
// 按钮尺寸
.btn {
  &-sm {
    height: 24px;
    padding: 0 12px;
    font-size: $font-size-xs;
  }
  
  &-md {
    height: 32px;
    padding: 0 16px;
    font-size: $font-size-sm;
  }
  
  &-lg {
    height: 40px;
    padding: 0 20px;
    font-size: $font-size-base;
  }
}

// 按钮类型
.btn {
  &-primary {
    background-color: $primary-color;
    color: white;
  }
  
  &-secondary {
    background-color: white;
    border: 1px solid $primary-color;
    color: $primary-color;
  }
  
  &-text {
    background: none;
    color: $primary-color;
  }
}
```

### 2.2 输入框规范

```scss
// 输入框尺寸
.input {
  &-sm {
    height: 24px;
    padding: 0 8px;
    font-size: $font-size-xs;
  }
  
  &-md {
    height: 32px;
    padding: 0 12px;
    font-size: $font-size-sm;
  }
  
  &-lg {
    height: 40px;
    padding: 0 16px;
    font-size: $font-size-base;
  }
}
```

## 3. 页面布局

### 3.1 通用布局

```html
<!-- 基础布局结构 -->
<div class="layout">
  <!-- 顶部导航 -->
  <header class="header">
    <div class="logo">MTDT</div>
    <nav class="nav">
      <!-- 导航内容 -->
    </nav>
    <div class="user-info">
      <!-- 用户信息 -->
    </div>
  </header>

  <!-- 主体内容 -->
  <main class="main">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <!-- 菜单内容 -->
    </aside>

    <!-- 内容区 -->
    <div class="content">
      <!-- 页面内容 -->
    </div>
  </main>
</div>
```

```scss
// 布局样式
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 64px;
  padding: 0 $spacing-lg;
  display: flex;
  align-items: center;
  background: white;
  box-shadow: $shadow-sm;
}

.main {
  flex: 1;
  display: flex;
}

.sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid $border-color;
}

.content {
  flex: 1;
  padding: $spacing-lg;
  background: $background;
}
```

## 4. 页面设计

### 4.1 登录页面

#### 4.1.1 页面布局
```html
<div class="login-container">
  <!-- Cesium地球容器 -->
  <div ref="cesiumContainer" class="earth-container"></div>
  
  <!-- 登录框 -->
  <div class="login-box">
    <div class="logo-section">
      <div class="logo-placeholder">
        <img src="/right-logo.png" alt="Logo" class="logo-image" />
      </div>
      <h1>山思数字平台</h1>
      <p>Mountain Tech. Digital Platform V0.1</p>
    </div>
    
    <el-form :model="loginForm" :rules="rules" ref="formRef">
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          placeholder="用户名"
          size="large"
          prefix-icon="User"
        />
      </el-form-item>
      
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          placeholder="密码"
          size="large"
          prefix-icon="Lock"
          show-password
        />
      </el-form-item>
      
      <el-form-item>
        <el-button
          type="primary"
          size="large"
          style="width: 100%"
          :loading="loading"
        >
          登录
        </el-button>
      </el-form-item>
    </el-form>
    
    <div class="demo-account">
      <p>测试账号：</p>
      <p>用户名：admin，密码：123456</p>
    </div>
  </div>
</div>
```

#### 4.1.2 样式定义
```scss
.login-container {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.earth-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: #000;
}

.login-box {
  position: absolute;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 400px;
  z-index: 2;
  top: 20px;
  right: 20px;
}

.logo-section {
  text-align: center;
  margin-bottom: 32px;
  
  .logo-placeholder {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    overflow: hidden;
    
    .logo-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  p {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.demo-account {
  margin-top: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;
  font-size: 12px;
  color: #666;
  
  p {
    margin: 2px 0;
  }
}
```

### 4.2 主页面

#### 4.2.1 页面布局
```html
<div class="main-layout">
  <!-- 顶部导航栏 -->
  <header class="app-header">
    <div class="header-left">
      <img src="/left-logo.png" alt="左侧logo" class="logo-image left-logo">
    </div>
    
    <div class="header-center">
      <img src="/right-logo.png" alt="右侧logo" class="logo-image">
      <h1 class="header-title">山思数字平台V0.1</h1>
    </div>
    
    <div class="header-right">
      <WeatherBar />
    </div>
  </header>
  
  <!-- 主要内容区域 -->
  <main class="main-content">
    <!-- 左侧工具栏 -->
    <ToolBar 
      :active-tool="activeTool"
      @tool-change="handleToolChange"
    />
    
    <!-- 地图容器 -->
    <div class="map-container">
      <CesiumViewer />
    </div>
    
    <!-- 右侧控制面板 -->
    <MapControls />
    
    <!-- 侧边栏面板系统 -->
    <SidePanel
      :is-open="showMapManagementPanel"
      title="地图管理"
      @close="showMapManagementPanel = false"
    >
      <MapManagementPanel />
    </SidePanel>

    <SidePanel
      :is-open="showLayerPanel"
      title="图层管理"
      @close="showLayerPanel = false"
    >
      <LayerPanel />
    </SidePanel>
    
    <!-- 其他功能面板 -->
    <SidePanel
      :is-open="showProtectionPanel"
      title="保护区域管理"
      @close="showProtectionPanel = false"
    >
      <ProtectionAreaPanel />
    </SidePanel>
    
    <!-- 测量面板 -->
    <MeasurePanel
      v-if="showMeasurePanel"
      :measure-results="measureResults"
      :active-measure-type="activeMeasureType"
      @close="closeMeasurePanel"
    />
    
    <!-- 绘图面板 -->
    <DrawPanel
      v-if="showDrawPanel"
      :draw-results="drawResults"
      :active-draw-type="activeDrawType"
      @close="closeDrawPanel"
    />
    
    <!-- 信息面板 -->
    <InfoPanel 
      v-if="showInfoPanel"
      :selected-area="selectedArea"
      @close="showInfoPanel = false"
    />
  </main>
</div>
```

#### 4.2.2 样式定义
```scss
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #000;
  overflow: hidden;
}

.app-header {
  height: 56px;
  background: linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.9)), 
              url('@/assets/header-bg.jpg') center/cover;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.8);
  padding: 0 16px;
  border-radius: 8px;
}

.header-right {
  width: 320px;
  overflow: hidden;
}

.logo-image {
  height: 32px;
  object-fit: contain;
}

.left-logo {
  width: 80px;
  height: auto;
}

.header-title {
  font-family: 'KaiTi', 'STKaiti', serif;
  font-size: 40px;
  font-weight: 700;
  color: #1a365d;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
  letter-spacing: 1px;
  line-height: 1.2;
}

.main-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.map-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
```

#### 4.2.3 组件样式

```scss
// 工具栏组件样式
.tool-bar {
  position: absolute;
  left: 16px;
  top: 16px;
  z-index: 100;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .tool-button {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      background: #f3f4f6;
    }
    
    &.active {
      background: #e0f2fe;
      color: #0284c7;
    }
    
    .icon {
      font-size: 20px;
    }
  }
}

// 侧边面板组件样式
.side-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 360px;
  max-height: calc(100% - 32px);
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .panel-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
    
    .close-button {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      
      &:hover {
        background: #f3f4f6;
      }
    }
  }
  
  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }
}

// 测量面板样式
.measure-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  
  .measure-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .measure-tools {
    padding: 12px;
    display: flex;
    gap: 8px;
    border-bottom: 1px solid #e5e7eb;
    
    .measure-tool-button {
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      
      &:hover {
        background: #f3f4f6;
      }
      
      &.active {
        background: #e0f2fe;
        color: #0284c7;
      }
    }
  }
  
  .measure-results {
    max-height: 300px;
    overflow-y: auto;
    padding: 12px;
    
    .measure-result-item {
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 8px;
      background: #f9fafb;
      
      .result-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
      }
      
      .result-value {
        font-weight: 600;
      }
      
      .result-actions {
        display: flex;
        gap: 8px;
        margin-top: 4px;
      }
    }
  }
}

// 地图控制组件样式
.map-controls {
  position: absolute;
  right: 16px;
  bottom: 16px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .control-group {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    
    .control-button {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-bottom: 1px solid #e5e7eb;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background: #f3f4f6;
      }
    }
  }
}

## 5. 响应式设计

### 5.1 断点定义

```scss
// 断点变量
$breakpoints: (
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1600px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}
```

### 5.2 响应式布局

```scss
// 响应式布局示例
.layout {
  .sidebar {
    @include respond-to('sm') {
      width: 200px;
    }
    
    @include respond-to('md') {
      width: 240px;
    }
    
    @include respond-to('lg') {
      width: 280px;
    }
  }
  
  .content {
    padding: $spacing-md;
    
    @include respond-to('md') {
      padding: $spacing-lg;
    }
  }
}
```

## 6. 主题定制

### 6.1 主题变量

```scss
// 主题配置
$themes: (
  'light': (
    'background': #F5F5F5,
    'text-primary': #262626,
    'text-secondary': #595959,
    'border-color': #D9D9D9,
  ),
  'dark': (
    'background': #1F1F1F,
    'text-primary': #FFFFFF,
    'text-secondary': #A6A6A6,
    'border-color': #434343,
  )
);

// 主题混入
@mixin themed() {
  @each $theme, $map in $themes {
    .theme-#{$theme} & {
      $theme-map: () !global;
      @each $key, $value in $map {
        $theme-map: map-merge($theme-map, ($key: $value)) !global;
      }
      @content;
      $theme-map: null !global;
    }
  }
}
```

### 6.2 主题使用

```scss
// 使用主题
.content {
  @include themed() {
    background-color: themed('background');
    color: themed('text-primary');
  }
}
```

## 7. 总结

本UI设计规范文档提供了山思数字平台的完整界面设计方案，包括：

1. **设计系统**
   - 色彩系统
   - 字体规范
   - 间距规范
   - 阴影规范

2. **组件规范**
   - 按钮规范
   - 输入框规范
   - 其他通用组件

3. **页面设计**
   - 登录页面
   - 主页面
   - 响应式设计

4. **主题定制**
   - 主题变量
   - 主题切换

建议前端开发团队在开发过程中严格遵循本规范，确保整个平台的界面风格统一和用户体验的一致性。同时，也要根据实际开发需求和用户反馈，持续完善和更新本规范。