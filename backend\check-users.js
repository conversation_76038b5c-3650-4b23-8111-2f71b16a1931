const { Client } = require('pg');
require('dotenv').config();

async function checkUsers() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'mtdt_db',
  });

  try {
    await client.connect();
    console.log('✅ 连接到数据库成功');

    // 检查用户表
    const usersResult = await client.query('SELECT COUNT(*) as count FROM system.users;');
    console.log(`📊 用户表中有 ${usersResult.rows[0].count} 个用户`);

    // 检查角色表
    const rolesResult = await client.query('SELECT COUNT(*) as count FROM system.roles;');
    console.log(`📊 角色表中有 ${rolesResult.rows[0].count} 个角色`);

    // 列出所有用户
    if (usersResult.rows[0].count > 0) {
      const allUsers = await client.query('SELECT username, email, is_active, created_at FROM system.users;');
      console.log('\n👥 现有用户列表:');
      allUsers.rows.forEach(user => {
        console.log(`  - ${user.username} (${user.email}) - ${user.is_active ? '激活' : '未激活'} - 创建于: ${user.created_at}`);
      });
    } else {
      console.log('\n❌ 数据库中没有用户数据！');
    }

  } catch (error) {
    console.error('❌ 检查用户失败:', error.message);
  } finally {
    await client.end();
  }
}

checkUsers();
