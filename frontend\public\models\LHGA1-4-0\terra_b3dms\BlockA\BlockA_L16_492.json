{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.8970781564712524, "root": {"boundingVolume": {"box": [10130.61328125, 4351.15576171875, -347.2560119628906, 148.43701171875, 0.0, 0.0, 0.0, 82.74755859375, 0.0, 0.0, 0.0, 95.07271575927734]}, "children": [{"boundingVolume": {"box": [10056.39453125, 4351.15576171875, -347.2560119628906, 74.21826171875, 0.0, 0.0, 0.0, 82.74755859375, 0.0, 0.0, 0.0, 95.07271575927734]}, "children": [{"boundingVolume": {"box": [10046.6796875, 4332.2763671875, -394.7923583984375, 64.50244140625, 0.0, 0.0, 0.0, 63.867919921875, 0.0, 0.0, 0.0, 47.53636169433594]}, "children": [{"boundingVolume": {"box": [10014.322265625, 4300.32275390625, -407.83782958984375, 32.14501953125, 0.0, 0.0, 0.0, 31.91455078125, 0.0, 0.0, 0.0, 34.49089050292969]}, "content": {"uri": "BlockA_L19_74.b3dm"}, "geometricError": 0.1327802538871765, "refine": "REPLACE"}, {"boundingVolume": {"box": [10014.322265625, 4364.1513671875, -381.65814208984375, 32.14501953125, 0.0, 0.0, 0.0, 31.914306640625, 0.0, 0.0, 0.0, 34.402130126953125]}, "content": {"uri": "BlockA_L19_73.b3dm"}, "geometricError": 0.12578892707824707, "refine": "REPLACE"}, {"boundingVolume": {"box": [10078.611328125, 4331.51806640625, -374.5034484863281, 32.14501953125, 0.0, 0.0, 0.0, 63.10986328125, 0.0, 0.0, 0.0, 27.2474365234375]}, "content": {"uri": "BlockA_L19_72.b3dm"}, "geometricError": 0.12235485762357712, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1880.b3dm"}, "geometricError": 0.2527060806751251, "refine": "REPLACE"}, {"boundingVolume": {"box": [10056.39453125, 4351.15576171875, -299.71966552734375, 74.21826171875, 0.0, 0.0, 0.0, 82.74755859375, 0.0, 0.0, 0.0, 47.536354064941406]}, "children": [{"boundingVolume": {"box": [10104.458984375, 4323.5732421875, -320.64422607421875, 26.154296875, 0.0, 0.0, 0.0, 55.1650390625, 0.0, 0.0, 0.0, 26.611770629882812]}, "content": {"uri": "BlockA_L19_71.b3dm"}, "geometricError": 0.11192993819713593, "refine": "REPLACE"}, {"boundingVolume": {"box": [10019.28515625, 4406.50927734375, -299.71966552734375, 37.10888671875, 0.0, 0.0, 0.0, 27.39404296875, 0.0, 0.0, 0.0, 47.536354064941406]}, "content": {"uri": "BlockA_L19_70.b3dm"}, "geometricError": 0.10895337909460068, "refine": "REPLACE"}, {"boundingVolume": {"box": [10093.50390625, 4406.32080078125, -300.4792175292969, 37.109375, 0.0, 0.0, 0.0, 27.58251953125, 0.0, 0.0, 0.0, 46.77679443359375]}, "content": {"uri": "BlockA_L19_69.b3dm"}, "geometricError": 0.10560092329978943, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1879.b3dm"}, "geometricError": 0.21840517222881317, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_977.b3dm"}, "geometricError": 0.4697711765766144, "refine": "REPLACE"}, {"boundingVolume": {"box": [10204.83203125, 4351.15576171875, -323.4232482910156, 74.21875, 0.0, 0.0, 0.0, 82.74755859375, 0.0, 0.0, 0.0, 65.59207153320312]}, "children": [{"boundingVolume": {"box": [10204.83203125, 4309.7822265625, -302.216064453125, 74.21875, 0.0, 0.0, 0.0, 41.373779296875, 0.0, 0.0, 0.0, 44.38487243652344]}, "children": [{"boundingVolume": {"box": [10167.72265625, 4309.7822265625, -291.6808776855469, 37.109375, 0.0, 0.0, 0.0, 41.373779296875, 0.0, 0.0, 0.0, 33.849700927734375]}, "content": {"uri": "BlockA_L19_68.b3dm"}, "geometricError": 0.09891723096370697, "refine": "REPLACE"}, {"boundingVolume": {"box": [10241.94140625, 4309.7822265625, -308.85760498046875, 37.109375, 0.0, 0.0, 0.0, 41.373779296875, 0.0, 0.0, 0.0, 37.743316650390625]}, "content": {"uri": "BlockA_L19_67.b3dm"}, "geometricError": 0.10547652095556259, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1878.b3dm"}, "geometricError": 0.2040082961320877, "refine": "REPLACE"}, {"boundingVolume": {"box": [10204.83203125, 4392.529296875, -324.9939880371094, 74.21875, 0.0, 0.0, 0.0, 41.373779296875, 0.0, 0.0, 0.0, 64.02133178710938]}, "children": [{"boundingVolume": {"box": [10167.72265625, 4392.529296875, -295.16021728515625, 37.109375, 0.0, 0.0, 0.0, 41.373779296875, 0.0, 0.0, 0.0, 34.10932922363281]}, "content": {"uri": "BlockA_L19_66.b3dm"}, "geometricError": 0.10183175653219223, "refine": "REPLACE"}, {"boundingVolume": {"box": [10241.94140625, 4392.529296875, -332.0697326660156, 37.109375, 0.0, 0.0, 0.0, 41.373779296875, 0.0, 0.0, 0.0, 56.9246826171875]}, "content": {"uri": "BlockA_L19_65.b3dm"}, "geometricError": 0.11352970451116562, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1877.b3dm"}, "geometricError": 0.21430161595344543, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_976.b3dm"}, "geometricError": 0.4181983768939972, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_492.b3dm"}, "geometricError": 0.8970781564712524, "refine": "REPLACE"}}