// 兼容TypeORM等依赖crypto的场景
const crypto = require('crypto');
// global.crypto = crypto; // Node.js 20+ 已内置 globalThis.crypto，无需赋值
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import helmet from 'helmet';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.use(helmet());

  // 从环境变量获取CORS配置
  const corsOrigins = process.env.CORS_ORIGIN
    ? process.env.CORS_ORIGIN.split(',').map(origin => origin.trim())
    : ['http://localhost:5173', 'http://127.0.0.1:5173'];

  app.enableCors({
    origin: corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  });
  app.setGlobalPrefix('api');
  await app.listen(process.env.PORT || 3000, '0.0.0.0');
  console.log(`应用正在运行: http://localhost:${process.env.PORT || 3000}`);
  console.log(`CORS允许的源: ${corsOrigins.join(', ')}`);
}
bootstrap();
