# 外网访问专用配置
# 基于 docker-compose.internal.yml，增加安全配置

services:

  # PostgreSQL数据库 - 不对外暴露
  postgres:
    image: postgres:15-alpine
    container_name: mtdt-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    # 不暴露端口到外网
    networks:
      - mtdt-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存 - 不对外暴露
  redis:
    image: redis:7-alpine
    container_name: mtdt-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    # 不暴露端口到外网
    networks:
      - mtdt-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mtdt-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      # 数据库配置
      DB_HOST: postgres
      DB_PORT: 5432
      DB_DATABASE: ${POSTGRES_DB}
      DB_USERNAME: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      DB_SYNCHRONIZE: "true"   # 临时启用自动同步以创建表结构
      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      # JWT配置
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN}
      # 邮件配置
      MAIL_HOST: smtp.pfi.org.cn
      MAIL_PORT: 465
      MAIL_USER: <EMAIL>
      MAIL_PASS: Welcome@314
      # 安全配置
      CORS_ORIGIN: ${CORS_ORIGIN}
      RATE_LIMIT_WINDOW: 900000  # 15分钟
      RATE_LIMIT_MAX: 100        # 每15分钟最多100次请求
    ports:
      - "0.0.0.0:8000:3000"  # 使用非标准端口
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mtdt-network
    volumes:
      - ./backend/uploads:/app/uploads
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mtdt-frontend
    restart: unless-stopped
    ports:
      - "0.0.0.0:8080:80"    # 使用非标准端口
      - "0.0.0.0:8443:443"   # HTTPS端口
    depends_on:
      - backend
    networks:
      - mtdt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3



volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  mtdt-network:
    driver: bridge
