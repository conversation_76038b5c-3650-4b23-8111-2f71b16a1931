# MTDT平台外网访问配置说明书

## 📋 概述

本文档用于指导网络管理员配置路由器端口转发，以实现MTDT平台的外网访问功能。

## 🌐 系统信息

### 服务器信息
- **内网IP地址**：`*************`
- **公网IP地址**：`**************`
- **操作系统**：Windows
- **部署方式**：Docker容器化部署

### 服务端口映射
| 服务类型 | 容器端口 | 主机端口 | 外网端口 | 说明 |
|---------|---------|---------|---------|------|
| 前端HTTP | 80 | 8080 | 8080 | 主要访问端口 |
| 前端HTTPS | 443 | 8443 | 8443 | 安全访问端口 |
| 后端API | 3000 | 8000 | 8000 | API服务端口 |

## 🔧 路由器端口转发配置

### 配置要求
请在路由器管理界面中添加以下三条端口转发规则：

#### 规则1：前端HTTP服务
```
服务名称：MTDT-Frontend-HTTP
外部端口：8080
内部IP地址：*************
内部端口：8080
协议：TCP
状态：启用
```

#### 规则2：前端HTTPS服务
```
服务名称：MTDT-Frontend-HTTPS
外部端口：8443
内部IP地址：*************
内部端口：8443
协议：TCP
状态：启用
```

#### 规则3：后端API服务
```
服务名称：MTDT-Backend-API
外部端口：8000
内部IP地址：*************
内部端口：8000
协议：TCP
状态：启用
```

### 常见路由器配置路径

#### TP-Link路由器
```
登录路由器 → 高级设置 → NAT转发 → 虚拟服务器 → 添加
```

#### 华为/荣耀路由器
```
登录路由器 → 更多功能 → 安全设置 → 虚拟服务器 → 添加
```

#### 小米路由器
```
登录路由器 → 高级设置 → 端口转发 → 添加规则
```

#### 华硕路由器
```
登录路由器 → 外部网络(WAN) → 虚拟服务器/端口转发 → 添加
```

#### 网件(NETGEAR)路由器
```
登录路由器 → 动态DNS → 端口转发/端口触发 → 添加服务
```

## 🔒 防火墙配置

### Windows防火墙
如果服务器启用了Windows防火墙，请确保以下端口被允许：

```powershell
# 以管理员身份运行PowerShell，执行以下命令：
New-NetFirewallRule -DisplayName "MTDT-Frontend-8080" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow
New-NetFirewallRule -DisplayName "MTDT-Frontend-8443" -Direction Inbound -Protocol TCP -LocalPort 8443 -Action Allow
New-NetFirewallRule -DisplayName "MTDT-Backend-8000" -Direction Inbound -Protocol TCP -LocalPort 8000 -Action Allow
```

### 第三方防火墙
如果使用第三方防火墙软件，请在防火墙中添加以下端口的入站规则：
- TCP 8080
- TCP 8443  
- TCP 8000

## ✅ 配置验证

### 内网验证
配置完成后，首先在内网环境下验证：
```
http://*************:8080
```
确认可以正常访问MTDT平台。

### 外网验证
使用外网环境（如手机4G网络）访问：
```
http://**************:8080
```

### 功能测试
1. **访问测试**：确认页面能够正常加载
2. **登录测试**：使用管理员账户登录
   - 用户名：`lhgadmin`
   - 密码：`11111111`
3. **注册测试**：测试新用户注册功能
4. **API测试**：确认前后端通信正常

## 🚨 安全注意事项

### 端口安全
- 使用非标准端口（8080, 8443, 8000）提高安全性
- 数据库端口（5432）和Redis端口（6379）未暴露到外网
- 建议定期更改管理员密码

### 访问控制
- 系统内置速率限制：每15分钟最多100次请求
- 支持账户锁定机制防止暴力破解
- 所有操作都有审计日志记录

### 监控建议
- 定期检查访问日志
- 监控异常访问模式
- 及时更新系统安全补丁

## 📞 技术支持

### 系统管理
- **管理员账户**：lhgadmin / 11111111
- **系统日志**：可通过Docker命令查看
- **服务重启**：如需重启服务，请联系系统管理员

### 故障排查
如果外网访问出现问题，请按以下顺序检查：
1. 确认内网访问正常
2. 检查路由器端口转发配置
3. 验证防火墙设置
4. 确认公网IP地址未变更
5. 检查服务器服务状态

### 联系方式
如需技术支持，请提供以下信息：
- 具体错误信息或现象
- 访问时间和IP地址
- 浏览器类型和版本
- 网络环境（内网/外网）

---

**配置完成后，MTDT平台将可通过 http://**************:8080 进行外网访问**
