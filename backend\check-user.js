const { DataSource } = require('typeorm');
const bcrypt = require('bcryptjs');

// 数据库配置
const dataSource = new DataSource({
  type: 'postgres',
  host: 'localhost',
  port: 5432,
  username: 'testUser',
  password: '1234',
  database: 'webgis_db',
  synchronize: false,
  logging: true,
});

async function checkUser() {
  try {
    await dataSource.initialize();
    console.log('数据库连接成功');

    // 查询test1用户
    const query = `
      SELECT 
        user_id,
        username, 
        email, 
        password_hash,
        is_active,
        login_attempts,
        locked_until,
        created_at
      FROM system.users 
      WHERE username = 'test1'
    `;
    
    const result = await dataSource.query(query);
    
    if (result.length === 0) {
      console.log('❌ 用户 test1 不存在');
    } else {
      const user = result[0];
      console.log('✅ 找到用户 test1:');
      console.log('用户ID:', user.user_id);
      console.log('用户名:', user.username);
      console.log('邮箱:', user.email);
      console.log('是否激活:', user.is_active);
      console.log('登录尝试次数:', user.login_attempts);
      console.log('锁定到:', user.locked_until);
      console.log('创建时间:', user.created_at);
      console.log('密码哈希:', user.password_hash.substring(0, 20) + '...');
      
      // 测试密码验证
      const testPassword = '12345678';
      const isValid = await bcrypt.compare(testPassword, user.password_hash);
      console.log(`密码 "${testPassword}" 验证结果:`, isValid ? '✅ 正确' : '❌ 错误');
    }
    
    // 查询所有用户
    const allUsersQuery = 'SELECT username, email, is_active FROM system.users ORDER BY created_at DESC LIMIT 5';
    const allUsers = await dataSource.query(allUsersQuery);
    console.log('\n最近创建的用户:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.username} (${user.email}) - 激活: ${user.is_active}`);
    });
    
  } catch (error) {
    console.error('错误:', error.message);
  } finally {
    await dataSource.destroy();
  }
}

checkUser();