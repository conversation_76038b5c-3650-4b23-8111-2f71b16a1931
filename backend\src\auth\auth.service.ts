import { Injectable, UnauthorizedException, BadRequestException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User } from '../entities/user.entity';
import { UserSession } from '../entities/user-session.entity';
import { AuditLog } from '../entities/audit-log.entity';
import { LoginDto, ChangePasswordDto, RegisterDto, EmailLoginDto } from './dto/login.dto';
import { Role } from '../entities/role.entity';
import { JwtPayload } from './strategies/jwt.strategy';
import { MailService } from '../mail/mail.service';

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    username: string;
    email: string;
    fullName: string;
    roles: string[];
    permissions: string[];
    isActive: boolean;
  };
  expiresIn: number;
}

export interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: number;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserSession)
    private readonly sessionRepository: Repository<UserSession>,
    @InjectRepository(AuditLog)
    private readonly auditRepository: Repository<AuditLog>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    private readonly jwtService: JwtService,
    private readonly mailService: MailService, // Inject MailService
  ) {}

  /**
   * 验证用户凭据（用于 local strategy）
   */
  async validateUser(username: string, password: string): Promise<User | null> {
    try {
      this.logger.log(`Attempting to validate user: ${username}`);
      
      const user = await this.userRepository.findOne({
        where: { username },
        relations: ['roles'],
      });

      if (!user) {
        this.logger.warn(`User not found: ${username}`);
        await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_USERNAME', undefined, username);
        return null;
      }

      this.logger.log(`User found: ${user.username}, active: ${user.isActive}, locked: ${user.isLocked}`);

      // 检查账户是否被锁定
      if (user.isLocked) {
        await this.logSecurityEvent('LOGIN_ATTEMPT_LOCKED_ACCOUNT', user.id, username);
        return null;
      }

      // 检查账户是否激活
      if (!user.isActive) {
        await this.logSecurityEvent('LOGIN_ATTEMPT_INACTIVE_ACCOUNT', user.id, username);
        return null;
      }

      // 验证密码
      this.logger.log(`Validating password for user: ${user.username}`);
      this.logger.log(`Stored password hash: ${user.passwordHash.substring(0, 20)}...`);
      this.logger.log(`Input password: ${password}`);
      
      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      this.logger.log(`Password validation result: ${isPasswordValid}`);
      
      if (!isPasswordValid) {
        // 增加失败登录次数
        await this.handleFailedLogin(user);
        await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_PASSWORD', user.id, username);
        return null;
      }

      // 重置失败登录次数
      if (user.failedLoginAttempts > 0) {
        await this.userRepository.update(user.id, {
          login_attempts: 0,
          locked_until: null,
        });
      }

      await this.logSecurityEvent('LOGIN_SUCCESS', user.id, username);
      return user;
    } catch (error) {
      this.logger.error(`Error validating user: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto, userAgent?: string, ipAddress?: string): Promise<LoginResponse> {
    const { username, password } = loginDto;

    const user = await this.validateUser(username, password);
    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 生成访问令牌和刷新令牌
    const tokens = await this.generateTokens(user);

    // 创建用户会话
    await this.createUserSession(user, tokens.refreshToken, userAgent, ipAddress);

    // 更新最后登录时间
    await this.userRepository.update(user.id, {
      last_login: new Date(),
    });

    // 获取用户权限
    const permissions = this.getUserPermissions(user);

    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        roles: user.roles.map(role => role.name),
        permissions,
        isActive: user.isActive,
      },
      expiresIn: 3600, // 1 hour
    };
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: process.env.JWT_REFRESH_SECRET,
      });

      const session = await this.sessionRepository.findOne({
        where: { 
          refresh_token: refreshToken,
        },
        relations: ['user', 'user.roles'],
      });

      if (!session || session.expiresAt < new Date()) {
        throw new UnauthorizedException('刷新令牌无效或已过期');
      }

      if (!session.user.isActive) {
        throw new UnauthorizedException('用户账户已被禁用');
      }

      // 生成新的访问令牌
      const accessToken = await this.generateAccessToken(session.user);

      await this.logSecurityEvent('TOKEN_REFRESH', session.user.id);

      return {
        accessToken,
        expiresIn: 3600,
      };
    } catch (error) {
      this.logger.error(`Error refreshing token: ${error.message}`, error.stack);
      throw new UnauthorizedException('刷新令牌无效');
    }
  }

  /**
   * 用户登出
   */
  async logout(refreshToken: string): Promise<void> {
    try {
      const session = await this.sessionRepository.findOne({
        where: { refresh_token: refreshToken },
        relations: ['user'],
      });

      if (session) {
        await this.sessionRepository.update(session.id, {
          expires_at: new Date(), // 设置为已过期
        });

        await this.logSecurityEvent('LOGOUT', session.user.id);
      }
    } catch (error) {
      this.logger.error(`Error during logout: ${error.message}`, error.stack);
    }
  }

  /**
   * 登出所有设备
   */
  async logoutAllDevices(userId: string): Promise<void> {
    try {
      await this.sessionRepository.update(
        { user_id: userId },
        { expires_at: new Date() } // 设置所有会话为已过期
      );

      await this.logSecurityEvent('LOGOUT_ALL_DEVICES', userId);
    } catch (error) {
      this.logger.error(`Error logging out all devices: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 修改密码
   */
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.userRepository.findOne({
      where: { user_id: userId },
    });

    if (!user) {
      throw new BadRequestException('用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      await this.logSecurityEvent('PASSWORD_CHANGE_FAILED_WRONG_CURRENT', userId);
      throw new BadRequestException('当前密码错误');
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await bcrypt.compare(newPassword, user.passwordHash);
    if (isSamePassword) {
      throw new BadRequestException('新密码不能与当前密码相同');
    }

    // 生成新密码哈希
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await this.userRepository.update(userId, {
      password_hash: newPasswordHash,
      password_changed_at: new Date(),
    });

    // 记录密码更改事件
    await this.logSecurityEvent('PASSWORD_CHANGED', userId);

    // 登出所有设备（强制重新登录）
    await this.logoutAllDevices(userId);

    this.logger.log(`Password changed for user: ${user.username}`);
  }

  /**
   * 重置用户密码（管理员功能）
   */
  async resetPassword(username: string, newPassword: string): Promise<{ message: string }> {
    try {
      const user = await this.userRepository.findOne({
        where: { username },
      });

      if (!user) {
        throw new BadRequestException('用户不存在');
      }

      // 生成新密码哈希
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      await this.userRepository.update(user.user_id, {
        password_hash: newPasswordHash,
        password_changed_at: new Date(),
        login_attempts: 0, // 重置登录尝试次数
        locked_until: null, // 解除锁定
      });

      // 记录密码重置事件
      await this.logSecurityEvent('PASSWORD_RESET_BY_ADMIN', user.user_id, `Password reset by admin for user: ${username}`);

      // 登出所有设备（强制重新登录）
      await this.logoutAllDevices(user.user_id);

      this.logger.log(`Password reset successfully for user: ${username}`);
      
      return {
        message: `用户 ${username} 的密码已重置为: ${newPassword}`
      };
    } catch (error) {
      this.logger.error(`Error resetting password for user ${username}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 验证JWT载荷（用于 JWT strategy）
   */
  async validateJwtPayload(payload: JwtPayload): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { user_id: payload.sub },
      relations: ['roles'],
    });

    if (!user || !user.isActive) {
      throw new UnauthorizedException('用户不存在或已被禁用');
    }

    return user;
  }

  /**
   * 生成访问令牌和刷新令牌
   */
  private async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const payload: JwtPayload = {
      username: user.username,
      sub: user.id,
      roles: user.roles.map(role => role.name),
      permissions: this.getUserPermissions(user),
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: process.env.JWT_SECRET,
        expiresIn: '1h',
      }),
      this.jwtService.signAsync(
        { sub: user.id, type: 'refresh' },
        {
          secret: process.env.JWT_REFRESH_SECRET,
          expiresIn: '7d',
        }
      ),
    ]);

    return { accessToken, refreshToken };
  }

  /**
   * 生成访问令牌
   */
  private async generateAccessToken(user: User): Promise<string> {
    const payload: JwtPayload = {
      username: user.username,
      sub: user.id,
      roles: user.roles.map(role => role.name),
      permissions: this.getUserPermissions(user),
    };

    return this.jwtService.signAsync(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn: '1h',
    });
  }

  /**
   * 创建用户会话
   */
  private async createUserSession(
    user: User, 
    refreshToken: string, 
    userAgent?: string, 
    ipAddress?: string
  ): Promise<UserSession> {
    const session = this.sessionRepository.create({
      user_id: user.id,
      refresh_token: refreshToken,
      user_agent: userAgent,
      ip_address: ipAddress,
      created_at: new Date(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    return this.sessionRepository.save(session);
  }

  /**
   * 处理登录失败
   */
  private async handleFailedLogin(user: User): Promise<void> {
    const newFailedAttempts = user.failedLoginAttempts + 1;
    const maxAttempts = 5;
    const lockDurationMinutes = 15;

    const updateData: any = {
      login_attempts: newFailedAttempts,
    };

    if (newFailedAttempts >= maxAttempts) {
      updateData.locked_until = new Date(Date.now() + lockDurationMinutes * 60 * 1000);
    }

    await this.userRepository.update(user.id, updateData);
  }

  /**
   * 获取用户权限
   */
  private getUserPermissions(user: User): string[] {
    const permissions = new Set<string>();
    
    if (user.roles && Array.isArray(user.roles)) {
      user.roles.forEach(role => {
        const rolePermissions = role.getPermissions();
        rolePermissions.forEach(permission => {
          permissions.add(permission);
        });
      });
    }

    return Array.from(permissions);
  }

  /**
   * 记录安全事件
   */
  private async logSecurityEvent(
    action: string,
    userId?: string,
    details?: string
  ): Promise<void> {
    try {
      const auditLog = this.auditRepository.create({
        user_id: userId,
        action,
        resource: 'AUTH',
        details,
        created_at: new Date(),
      });

      await this.auditRepository.save(auditLog);
    } catch (error) {
      this.logger.error(`Error logging security event: ${error.message}`, error.stack);
    }
  }

  /**
   * 用户注册
   */
  async register(registerDto: RegisterDto): Promise<{
    message: string;
    user: {
      id: string;
      username: string;
      email: string;
      fullName: string;
    };
  }> {
    try {
      const { username, email, emailCode, password, confirmPassword, fullName, phone } = registerDto;

      // 验证密码确认
      if (password !== confirmPassword) {
        throw new BadRequestException('密码和确认密码不一致');
      }

      // 验证邮箱验证码
      const isCodeValid = await this.mailService.verifyCode(email, emailCode, 'register');
      if (!isCodeValid) {
        throw new BadRequestException('邮箱验证码无效或已过期');
      }

      // 检查用户名是否已存在
      const existingUserByUsername = await this.userRepository.findOne({
        where: { username },
      });

      if (existingUserByUsername) {
        throw new BadRequestException('用户名已被使用');
      }

      // 检查邮箱是否已存在
      const existingUserByEmail = await this.userRepository.findOne({
        where: { email },
      });

      if (existingUserByEmail) {
        throw new BadRequestException('邮箱已被使用');
      }

      // 获取默认用户角色（user）
      const viewerRole = await this.roleRepository.findOne({
        where: { name: 'user' },
      });

      if (!viewerRole) {
        throw new BadRequestException('默认用户角色不存在，请联系管理员');
      }

      // 创建新用户
      const user = new User();
      user.username = username;
      user.email = email;
      user.password = password; // 将通过实体的钩子自动加密
      user.full_name = fullName || '';
      user.phone = phone || '';
      user.is_active = true; // 注册后立即激活
      user.is_first_login = true;
      user.roles = [viewerRole];
      user.created_at = new Date();

      const savedUser = await this.userRepository.save(user);

      // 记录注册事件
      await this.logSecurityEvent('USER_REGISTER', savedUser.id, `User registered: ${username}`);

      this.logger.log(`User registered successfully: ${username}`);

      return {
        message: '用户注册成功',
        user: {
          id: savedUser.id,
          username: savedUser.username,
          email: savedUser.email,
          fullName: savedUser.fullName,
        },
      };
    } catch (error) {
      this.logger.error(`Error during user registration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 发送邮箱验证码
   */
  async sendEmailCode(email: string, type: 'login' | 'register' | 'reset' = 'login'): Promise<{ message: string }> {
    try {
      this.logger.log(`Sending email verification code to: ${email}`);

      // 检查用户是否存在
      const user = await this.userRepository.findOne({
        where: { email },
      });

      // 对于登录类型，检查用户是否存在
      if (type === 'login') {
        if (!user) {
          throw new BadRequestException('该邮箱地址未注册');
        }

        if (!user.isActive) {
          throw new BadRequestException('账户已被禁用');
        }

        if (user.isLocked) {
          throw new BadRequestException('账户已被锁定');
        }
      }

      // 对于注册类型，检查邮箱是否已被使用
      if (type === 'register') {
        if (user) {
          throw new BadRequestException('该邮箱地址已被注册');
        }
      }

      // 对于重置密码类型，检查用户是否存在
      if (type === 'reset') {
        if (!user) {
          throw new BadRequestException('该邮箱地址未注册');
        }

        if (!user.isActive) {
          throw new BadRequestException('账户已被禁用');
        }
      }

      // 发送验证码
      try {
        await this.mailService.sendVerificationCode(email, type);
        this.logger.log(`Email verification code sent successfully to: ${email}`);
      } catch (mailError) {
        this.logger.error(`Mail sending failed, but verification code generated: ${mailError.message}`);
        // 邮件发送失败，但验证码已生成，不抛出错误
      }

      return {
        message: '验证码已发送到您的邮箱，请查收'
      };
    } catch (error) {
      this.logger.error(`Error sending email verification code: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 验证邮箱验证码
   */
  async verifyEmailCode(email: string, code: string): Promise<boolean> {
    try {
      return await this.mailService.verifyCode(email, code);
    } catch (error) {
      this.logger.error(`Error verifying email code: ${error.message}`, error.stack);
      throw new BadRequestException('验证码验证失败');
    }
  }

  /**
   * 使用邮箱验证码登录
   */
  async loginWithEmail(emailLoginDto: EmailLoginDto): Promise<LoginResponse> {
    try {
      const { email, code } = emailLoginDto;
      
      this.logger.log(`Attempting email login for: ${email}`);

      // 验证验证码
      const isCodeValid = await this.verifyEmailCode(email, code);
      if (!isCodeValid) {
        await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_EMAIL_CODE', undefined, email);
        throw new UnauthorizedException('验证码无效或已过期');
      }

      // 查找用户
      const user = await this.userRepository.findOne({
        where: { email },
        relations: ['roles'],
      });

      if (!user) {
        await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_EMAIL', undefined, email);
        throw new UnauthorizedException('该邮箱地址未注册');
      }

      if (!user.isActive) {
        await this.logSecurityEvent('LOGIN_ATTEMPT_INACTIVE_ACCOUNT', user.id, email);
        throw new UnauthorizedException('账户已被禁用');
      }

      if (user.isLocked) {
        await this.logSecurityEvent('LOGIN_ATTEMPT_LOCKED_ACCOUNT', user.id, email);
        throw new UnauthorizedException('账户已被锁定');
      }

      // 重置失败登录次数
      if (user.failedLoginAttempts > 0) {
        await this.userRepository.update(user.id, {
          login_attempts: 0,
          locked_until: null,
        });
      }

      // 更新最后登录时间
      await this.userRepository.update(user.id, {
        last_login: new Date(),
      });

      // 生成令牌
      const tokens = await this.generateTokens(user);

      // 创建用户会话
      await this.createUserSession(user, tokens.refreshToken);

      // 记录登录成功事件
      await this.logSecurityEvent('EMAIL_LOGIN_SUCCESS', user.id, email);

      // 获取用户权限
      const permissions = this.getUserPermissions(user);

      this.logger.log(`Email login successful for user: ${user.username}`);

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          roles: user.roles.map(role => role.name),
          permissions,
          isActive: user.isActive,
        },
        expiresIn: 3600, // 1小时
      };
    } catch (error) {
      this.logger.error(`Error during email login: ${error.message}`, error.stack);
      throw error;
    }
  }
}