import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('user_sessions', { schema: 'system' })
export class UserSession {
  @PrimaryGeneratedColumn('uuid')
  session_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ length: 500 })
  refresh_token: string;

  @Column({ type: 'inet', nullable: true })
  ip_address: string;

  @Column({ type: 'text', nullable: true })
  user_agent: string;

  @Column({ type: 'timestamptz' })
  expires_at: Date;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  last_used_at: Date;

  // 关联关系
  @ManyToOne(() => User, user => user.sessions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // 虚拟字段 - 密码（用于接收明文密码）
  password?: string;

  // 驼峰命名的访问器
  get id(): string {
    return this.session_id;
  }

  get userId(): string {
    return this.user_id;
  }

  set userId(value: string) {
    this.user_id = value;
  }

  get refreshToken(): string {
    return this.refresh_token;
  }

  set refreshToken(value: string) {
    this.refresh_token = value;
  }

  get ipAddress(): string {
    return this.ip_address;
  }

  set ipAddress(value: string) {
    this.ip_address = value;
  }

  get userAgent(): string {
    return this.user_agent;
  }

  set userAgent(value: string) {
    this.user_agent = value;
  }

  get expiresAt(): Date {
    return this.expires_at;
  }

  set expiresAt(value: Date) {
    this.expires_at = value;
  }

  get createdAt(): Date {
    return this.created_at;
  }

  get lastUsedAt(): Date {
    return this.last_used_at;
  }

  set lastUsedAt(value: Date) {
    this.last_used_at = value;
  }

  get isActive(): boolean {
    return !this.isExpired();
  }

  get loggedOutAt(): Date | null {
    // 如果会话过期，返回过期时间作为登出时间
    return this.isExpired() ? this.expires_at : null;
  }

  // 实例方法
  isExpired(): boolean {
    return new Date() > this.expires_at;
  }

  updateLastUsed(): void {
    this.last_used_at = new Date();
  }

  // 转换为安全的输出格式
  toSafeObject() {
    return {
      session_id: this.session_id,
      user_id: this.user_id,
      ip_address: this.ip_address,
      user_agent: this.user_agent,
      expires_at: this.expires_at,
      created_at: this.created_at,
      last_used_at: this.last_used_at,
      is_expired: this.isExpired(),
    };
  }
}