# 图层标注系统使用指南

## 📋 概述

图层标注系统是一个完整的地图标注解决方案，支持在Cesium 3D地图上添加、编辑和管理各种类型的标注。

## 🎯 主要功能

### 1. 标注类型
- **文本标注**: 纯文本标注，适用于地名、说明等
- **图标标注**: 使用图标符号的标注，适用于POI标记
- **标注框**: 带背景框的文本标注，适用于重要信息提示

### 2. 标注管理
- 添加、编辑、删除标注
- 批量显示/隐藏操作
- 标注分组管理
- 样式自定义

### 3. 数据管理
- 导入/导出标注数据
- 本地存储自动保存
- 标注模板系统

## 🚀 快速开始

### 1. 打开标注面板
点击左侧工具栏中的标注图标 📋 打开标注管理面板。

### 2. 添加标注
1. 点击"添加标注"按钮
2. 选择标注类型（文本/图标/标注框）
3. 填写标注信息
4. 选择位置（点击地图/输入坐标/关联要素）
5. 设置样式
6. 保存标注

### 3. 编辑标注
1. 在标注列表中选择要编辑的标注
2. 点击编辑按钮
3. 修改标注属性
4. 保存更改

## 🎨 样式设置

### 文本标注样式
- 字体大小: 10-36px
- 文字颜色: 任意颜色
- 背景颜色: 可选
- 透明度: 0-100%

### 图标标注样式
- 图标类型: 星形、旗帜、警告、信息、问号
- 图标大小: 16-64px
- 图标颜色: 任意颜色

### 标注框样式
- 文字样式: 同文本标注
- 背景颜色: 可自定义
- 边框设置: 宽度、颜色、样式

## 📍 位置设置

### 1. 点击地图设置
启用标注模式后，直接点击地图上的位置添加标注。

### 2. 手动输入坐标
在标注编辑器中输入精确的经纬度坐标。

### 3. 关联要素
将标注关联到现有图层的要素上。

## 🔧 高级功能

### 1. 标注模板
- 预设常用样式模板
- 快速应用模板样式
- 自定义模板保存

### 2. 批量操作
- 批量显示/隐藏标注
- 批量删除标注
- 批量样式修改

### 3. 数据管理
- JSON格式导出标注数据
- 导入外部标注数据
- 自动本地存储

## 🎮 操作指南

### 标注模式
1. 点击工具栏中的标注按钮进入标注模式
2. 选择标注工具（文本/图标/标注框）
3. 点击地图添加标注
4. 再次点击标注按钮退出标注模式

### 标注交互
- **点击标注**: 选择并查看标注信息
- **悬停标注**: 显示标注预览
- **右键菜单**: 快速编辑、删除操作

### 快捷键
- `ESC`: 退出标注模式
- `Delete`: 删除选中的标注
- `Ctrl+Z`: 撤销操作
- `Ctrl+Y`: 重做操作

## 📊 标注统计

标注面板显示实时统计信息：
- 总标注数量
- 可见标注数量
- 隐藏标注数量
- 按类型分组统计

## 🔄 数据格式

### 导出格式
```json
{
  "annotations": [
    {
      "id": "annotation_1234567890",
      "type": "text",
      "title": "标注标题",
      "content": "标注内容",
      "layerId": "layer_id",
      "position": {...},
      "longitude": 104.1,
      "latitude": 30.6,
      "visible": true,
      "style": {
        "fontSize": 14,
        "textColor": "#000000",
        "backgroundColor": "#FFFFFF"
      },
      "createTime": "2024-01-01T00:00:00.000Z"
    }
  ],
  "exportTime": "2024-01-01T00:00:00.000Z"
}
```

## 🛠 API 接口

### 标注管理器
```typescript
// 初始化标注管理器
const annotationManager = new AnnotationManager(viewer, options)

// 添加标注
await annotationManager.addAnnotation(annotation)

// 删除标注
await annotationManager.removeAnnotation(annotationId)

// 更新标注
await annotationManager.updateAnnotation(annotation)

// 获取所有标注
const annotations = annotationManager.getAllAnnotations()
```

### Vue组合式API
```typescript
// 使用标注管理器
const {
  annotationManager,
  annotations,
  addAnnotation,
  removeAnnotation,
  updateAnnotation
} = useAnnotationManager()

// 初始化
initializeAnnotationManager(cesiumViewer)
```

## 🎯 最佳实践

### 1. 标注命名
- 使用有意义的标注标题
- 内容简洁明了
- 避免重复信息

### 2. 样式设计
- 保持样式一致性
- 使用对比度高的颜色
- 考虑地图背景色

### 3. 性能优化
- 避免添加过多标注（建议<1000个）
- 使用分组管理大量标注
- 及时清理不需要的标注

### 4. 数据管理
- 定期备份标注数据
- 使用有意义的文件名
- 保持数据格式一致

## 🐛 常见问题

### Q: 标注不显示怎么办？
A: 检查标注的visible属性，确保图层可见，检查位置坐标是否正确。

### Q: 如何批量修改标注样式？
A: 使用批量操作功能，或者导出数据后批量修改再导入。

### Q: 标注数据如何备份？
A: 使用导出功能将标注数据保存为JSON文件。

### Q: 如何提高标注性能？
A: 减少同时显示的标注数量，使用分组管理，避免复杂样式。

## 📞 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

---

*最后更新: 2024年1月*
