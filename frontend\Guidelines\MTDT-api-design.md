# 山思数字平台 API 设计规范

## 1. API 设计原则

### 1.1 基本原则

- RESTful API 设计风格
- 统一的错误处理机制
- 版本控制
- 安全性考虑
- 性能优化

### 1.2 URL 设计规范

#### 1.2.1 基础 URL 结构
```
https://api.mtdt.com/v1/{resource}/{id}
```

#### 1.2.2 命名规则
- 使用小写字母
- 使用连字符（-）连接单词
- 使用名词表示资源
- 避免使用动词

#### 1.2.3 当前API示例
```
# 认证相关API
POST /api/auth/send-email-code    # 发送邮箱验证码
POST /api/auth/register           # 用户注册
POST /api/auth/login              # 用户登录
POST /api/auth/login-email        # 邮箱验证码登录
GET  /api/auth/profile            # 获取用户信息

# 系统API
GET  /api/                        # 系统状态检查
GET  /api/health                  # 健康检查
```

### 1.3 HTTP 方法使用

- GET：获取资源
- POST：创建资源
- PUT：更新资源（全量更新）
- PATCH：更新资源（部分更新）
- DELETE：删除资源

## 2. 接口规范

### 2.1 请求格式

#### 2.1.1 Headers
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {token}",
  "X-Request-ID": "unique-request-id"
}
```

#### 2.1.2 查询参数
```
?page=1&size=10&sort=name&order=desc
```

#### 2.1.3 请求体格式
```json
{
  "data": {
    // 具体数据
  },
  "meta": {
    // 元数据
  }
}
```

### 2.2 响应格式

#### 2.2.1 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  },
  "meta": {
    "page": 1,
    "size": 10,
    "total": 100
  }
}
```

#### 2.2.2 错误响应
```json
{
  "code": 400,
  "message": "Invalid parameters",
  "errors": [
    {
      "field": "name",
      "message": "Name is required"
    }
  ]
}
```

### 2.3 状态码使用

- 200：成功
- 201：创建成功
- 204：删除成功
- 400：请求参数错误
- 401：未授权
- 403：禁止访问
- 404：资源不存在
- 500：服务器错误

## 3. 当前 API 定义

### 3.1 认证管理

#### 3.1.1 邮箱验证码
```typescript
// 发送邮箱验证码
POST /api/auth/send-email-code
Request {
  email: string;
  type: 'register' | 'login' | 'reset';
}
Response {
  code: 200;
  message: "验证码发送成功";
  data: {
    email: string;
    expiresIn: number; // 过期时间（秒）
  };
}
```

#### 3.1.2 用户注册
```typescript
// 用户注册
POST /api/auth/register
Request {
  username: string;
  email: string;
  password: string;
  code: string; // 邮箱验证码
}
Response {
  code: 201;
  message: "注册成功";
  data: {
    user: {
      id: string;
      username: string;
      email: string;
    };
    token: string;
  };
}
```

#### 3.1.3 用户登录
```typescript
// 用户名密码登录
POST /api/auth/login
Request {
  username: string;
  password: string;
}

// 邮箱验证码登录
POST /api/auth/login-email
Request {
  email: string;
  code: string; // 邮箱验证码
}

Response {
  code: 200;
  message: "登录成功";
  data: {
    user: {
      id: string;
      username: string;
      email: string;
    };
    token: string;
  };
}
```

### 3.2 系统状态

#### 3.2.1 健康检查
```typescript
// 系统状态检查
GET /api/
Response {
  code: 200;
  message: "MTDT API is running";
  data: {
    version: "1.0.0";
    timestamp: string;
  };
}

// 健康检查
GET /api/health
Response {
  code: 200;
  message: "Service is healthy";
  data: {
    status: "ok";
    database: "connected";
    redis: "connected";
    uptime: number;
  };
}
```

### 3.3 数据分析

#### 3.3.1 空间分析
```typescript
// 缓冲区分析
POST /api/v1/analysis/buffer
Request {
  geometry: GeoJSON.Geometry;
  distance: number;
  unit: 'meters' | 'kilometers';
}

// 叠加分析
POST /api/v1/analysis/overlay
Request {
  sourceLayer: string;
  targetLayer: string;
  operation: 'intersection' | 'union' | 'difference';
}
```

## 4. WebSocket API

### 4.1 实时数据推送

#### 4.1.1 连接建立
```
WS /api/v1/websocket
```

#### 4.1.2 消息格式
```typescript
interface WebSocketMessage {
  type: 'drone-status' | 'layer-update' | 'alert';
  data: any;
  timestamp: number;
}
```

## 5. 安全规范

### 5.1 认证

- 使用 JWT (JSON Web Token) 进行认证
- Token 在 Authorization header 中传递
- Token 过期时间设置为 2 小时
- 提供 refresh token 机制

### 5.2 权限控制

- 基于 RBAC (Role-Based Access Control) 模型
- 细粒度的资源访问控制
- API 级别的权限检查

### 5.3 数据加密

- 使用 HTTPS 进行传输加密
- 敏感数据存储加密
- 密码使用 bcrypt 加密

## 6. 性能优化

### 6.1 数据压缩

- 启用 GZIP 压缩
- 大型响应分页
- 按需加载数据

### 6.2 缓存策略

- 使用 ETags
- 合理设置 Cache-Control
- 实现数据缓存层

## 7. 开发流程

### 7.1 API 文档管理

- 使用 Swagger/OpenAPI 规范
- 自动生成 API 文档
- 文档版本控制

### 7.2 Mock 服务

- 提供 Mock 数据服务
- 支持自定义响应
- 模拟各种场景

### 7.3 测试策略

- 单元测试覆盖
- 接口自动化测试
- 性能测试

## 8. 错误处理

### 8.1 错误码定义

```typescript
enum ErrorCode {
  // 客户端错误 (400-499)
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  VALIDATION_ERROR = 422,
  
  // 服务器错误 (500-599)
  INTERNAL_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
  
  // 业务错误 (1000-9999)
  LAYER_NOT_FOUND = 1001,
  INVALID_GEOMETRY = 1002,
  DRONE_OFFLINE = 1003,
}
```

### 8.2 错误响应格式

```typescript
interface ErrorResponse {
  code: number;
  message: string;
  errors?: Array<{
    field?: string;
    message: string;
  }>;
  requestId?: string;
}
```

## 9. 版本控制

### 9.1 版本号规则

- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 9.2 版本管理策略

- URL 中包含版本号
- 支持多版本并行
- 版本升级计划
- 版本废弃通知

## 10. 监控和日志

### 10.1 监控指标

- 接口响应时间
- 错误率
- 并发请求数
- 资源使用率

### 10.2 日志记录

- 请求日志
- 错误日志
- 性能日志
- 安全日志

## 11. 示例代码

### 11.1 前端调用示例

```typescript
// 使用 axios 调用 API
import axios from 'axios';

const api = axios.create({
  baseURL: 'https://api.mtdt.com/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 添加认证拦截器
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 添加响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response) {
      // 处理错误响应
      const { code, message } = error.response.data;
      // 处理特定错误码
      if (code === 401) {
        // 处理未授权错误
      }
    }
    return Promise.reject(error);
  }
);

// API 调用示例
export const LayerAPI = {
  async getList() {
    return api.get('/layers');
  },
  
  async create(data) {
    return api.post('/layers', data);
  },
  
  async update(id, data) {
    return api.put(`/layers/${id}`, data);
  },
  
  async delete(id) {
    return api.delete(`/layers/${id}`);
  },
};
```

### 11.2 后端实现示例

```typescript
// 使用 Express 实现 API
import express from 'express';
import { validateToken } from './middleware/auth';
import { validateSchema } from './middleware/validation';
import { LayerService } from './services/layer';

const router = express.Router();

// 获取图层列表
router.get('/layers', 
  validateToken,
  async (req, res, next) => {
    try {
      const layers = await LayerService.getList();
      res.json({
        code: 200,
        message: 'success',
        data: { layers }
      });
    } catch (error) {
      next(error);
    }
  }
);

// 创建图层
router.post('/layers',
  validateToken,
  validateSchema('createLayer'),
  async (req, res, next) => {
    try {
      const layer = await LayerService.create(req.body);
      res.status(201).json({
        code: 201,
        message: 'Layer created successfully',
        data: { layer }
      });
    } catch (error) {
      next(error);
    }
  }
);
```

## 12. 总结

本API设计规范提供了一个完整的框架，用于指导前后端分离开发中的接口设计和实现。通过遵循这些规范，可以确保：

1. API的一致性和可预测性
2. 良好的安全性和性能
3. 便于维护和扩展
4. 清晰的文档和示例
5. 有效的错误处理和监控

团队应该定期审查和更新这些规范，以适应项目的发展和新的技术要求。