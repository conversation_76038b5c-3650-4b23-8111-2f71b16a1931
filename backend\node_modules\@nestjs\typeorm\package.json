{"name": "@nestjs/typeorm", "version": "10.0.2", "description": "Nest - modern, fast, powerful node.js web framework (@typeorm)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/typeorm#readme", "scripts": {"build": "rm -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "test:e2e:dev": "jest --config ./tests/jest-e2e.json --runInBand --watch", "prerelease": "npm run build", "release": "release-it", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "18.6.0", "@commitlint/config-angular": "18.6.0", "@nestjs/common": "10.3.2", "@nestjs/core": "10.3.2", "@nestjs/platform-express": "10.3.2", "@nestjs/testing": "10.3.2", "@types/jest": "29.5.12", "@types/node": "20.11.16", "@types/supertest": "6.0.2", "@types/uuid": "9.0.8", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "husky": "9.0.10", "jest": "29.7.0", "lint-staged": "15.2.2", "mysql": "2.18.1", "pg": "8.11.3", "prettier": "3.2.5", "reflect-metadata": "0.2.1", "release-it": "17.0.3", "rxjs": "7.8.1", "supertest": "6.3.4", "ts-jest": "29.1.2", "typeorm": "0.3.20", "typescript": "5.3.3"}, "dependencies": {"uuid": "9.0.1"}, "peerDependencies": {"@nestjs/common": "^8.0.0 || ^9.0.0 || ^10.0.0", "@nestjs/core": "^8.0.0 || ^9.0.0 || ^10.0.0", "reflect-metadata": "^0.1.13 || ^0.2.0", "rxjs": "^7.2.0", "typeorm": "^0.3.0"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/typeorm"}}