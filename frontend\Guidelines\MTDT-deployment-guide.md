# 山思数字平台部署指南

## 1. 部署架构

### 1.1 当前架构（Docker容器化部署）

```mermaid
graph TD
    A[用户访问] --> B[前端容器 :8080]
    B --> C[后端容器 :8000]
    C --> D[PostgreSQL容器 :5432]
    C --> E[Redis容器 :6379]
    F[Docker网络] --> B
    F --> C
    F --> D
    F --> E
```

### 1.2 系统要求

#### 1.2.1 最小配置
```yaml
# 开发/测试环境
minimum:
  cpu: 4核
  memory: 8GB
  disk: 50GB SSD
  os: Windows 10/11 或 Linux

# 生产环境
recommended:
  cpu: 8核
  memory: 16GB
  disk: 100GB SSD
  os: Windows Server 或 Linux
```

#### 1.2.2 测试环境
```yaml
# 服务器配置
test-frontend:
  cpu: 8核
  memory: 16GB
  disk: 200GB SSD
  os: Ubuntu 20.04

test-backend:
  cpu: 8核
  memory: 32GB
  disk: 300GB SSD
  os: Ubuntu 20.04

test-database:
  cpu: 16核
  memory: 64GB
  disk: 1TB SSD
  os: Ubuntu 20.04
```

#### 1.2.3 生产环境
```yaml
# 服务器配置
prod-frontend:
  cpu: 16核
  memory: 32GB
  disk: 500GB SSD
  os: Ubuntu 20.04
  count: 2

prod-backend:
  cpu: 16核
  memory: 64GB
  disk: 500GB SSD
  os: Ubuntu 20.04
  count: 2

prod-database:
  cpu: 32核
  memory: 128GB
  disk: 2TB SSD
  os: Ubuntu 20.04
  count: 3 # 1主2从
```

## 2. 环境准备

### 2.1 Docker环境安装

#### 2.1.1 Windows环境
```powershell
# 1. 安装Docker Desktop
# 下载地址：https://www.docker.com/products/docker-desktop

# 2. 启用WSL2（如果使用Windows）
wsl --install

# 3. 验证安装
docker --version
docker-compose --version
```

#### 2.1.2 Linux环境
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com | bash
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 2.2 网络配置

#### 2.2.1 防火墙配置
```bash
# UFW配置
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3000/tcp # 前端开发服务器
ufw allow 3001/tcp # 后端API服务器
ufw allow 5432/tcp # PostgreSQL
ufw allow 6379/tcp # Redis
```

#### 2.2.2 负载均衡配置
```nginx
# /etc/nginx/conf.d/mtdt.conf
upstream frontend {
    server ************:3000;
    server ************:3000;
}

upstream backend {
    server ************:3001;
    server ************:3001;
}

server {
    listen 80;
    server_name mtdt.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name mtdt.com;

    ssl_certificate /etc/nginx/ssl/mtdt.crt;
    ssl_certificate_key /etc/nginx/ssl/mtdt.key;

    # 前端配置
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # API配置
    location /api {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 3. 快速部署

### 3.1 获取项目代码

```bash
# 克隆项目（如果有Git仓库）
git clone <repository-url>
cd MTDT

# 或者直接使用现有项目目录
cd d:\MTDT\V107
```

### 3.2 环境配置

#### 3.2.1 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（重要！）
# 修改以下配置：
# - 数据库密码
# - JWT密钥
# - 邮件配置
```

#### 3.2.2 邮件配置示例
```env
# 邮件配置 - 阿里云企业邮箱
MAIL_HOST=smtp.pfi.org.cn
MAIL_PORT=465
MAIL_USER=<EMAIL>
MAIL_PASS=Welcome@314
```

### 3.3 部署方式选择

#### 3.3.1 内网部署（推荐用于开发/测试）
```bash
# 使用内网部署脚本
.\deploy-internal.bat

# 或者手动启动
docker-compose -f docker-compose.internal.yml up -d
```

#### 3.3.2 外网部署（推荐用于生产）
```bash
# 使用外网部署脚本
.\deploy-external.bat

# 或者手动启动
docker-compose -f docker-compose.external.yml up -d
```

#### 3.3.3 简单部署（默认配置）
```bash
# 使用默认配置
.\deploy-simple.bat

# 或者手动启动
docker-compose up -d
```

## 4. 服务配置详情

### 4.1 当前服务架构

```yaml
# docker-compose.yml 服务配置
services:
  frontend:
    container_name: mtdt-frontend
    ports: "0.0.0.0:8080:80"

  backend:
    container_name: mtdt-backend
    ports: "0.0.0.0:8000:3000"

  postgres:
    container_name: mtdt-postgres
    ports: "0.0.0.0:5432:5432"

  redis:
    container_name: mtdt-redis
    ports: "0.0.0.0:6379:6379"
```

### 4.2 访问地址

#### 4.2.1 本地访问
- **前端**: http://localhost:8080
- **后端API**: http://localhost:8000/api

#### 4.2.2 局域网访问
- **前端**: http://***********:8080
- **后端API**: http://***********:8000/api

#### 4.2.3 外网访问（需要路由器配置）
- **前端**: http://公网IP:8080
- **后端API**: http://公网IP:8000/api

### 3.3 数据库部署

#### 3.3.1 PostgreSQL配置
```bash
# PostgreSQL安装
apt install -y postgresql postgresql-contrib postgis

# 配置文件
cat > /etc/postgresql/13/main/postgresql.conf << EOF
listen_addresses = '*'
max_connections = 1000
shared_buffers = 4GB
effective_cache_size = 12GB
maintenance_work_mem = 1GB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 16MB
min_wal_size = 1GB
max_wal_size = 4GB
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
EOF

# 主从复制配置
cat > /etc/postgresql/13/main/pg_hba.conf << EOF
host    replication     replicator      ***********/24        md5
host    all            all             ***********/24        md5
EOF
```

#### 3.3.2 Redis配置
```bash
# Redis安装
apt install -y redis-server

# 配置文件
cat > /etc/redis/redis.conf << EOF
bind 0.0.0.0
protected-mode yes
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300
daemonize yes
supervised no
pidfile /var/run/redis/redis-server.pid
loglevel notice
logfile /var/log/redis/redis-server.log
databases 16
always-show-logo yes
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-disable-tcp-nodelay no
replica-priority 100
maxmemory 4gb
maxmemory-policy allkeys-lru
EOF
```

## 4. 监控配置

### 4.1 应用监控

#### 4.1.1 Prometheus配置
```yaml
# /etc/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'mtdt-frontend'
    static_configs:
      - targets: ['************:3000', '************:3000']

  - job_name: 'mtdt-backend'
    static_configs:
      - targets: ['************:3001', '************:3001']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['************:9100', '************:9100',
                 '************:9100', '************:9100']
```

#### 4.1.2 Grafana配置
```ini
# /etc/grafana/grafana.ini
[server]
http_port = 3000
domain = grafana.mtdt.com

[security]
admin_user = admin
admin_password = <secure-password>

[auth.anonymous]
enabled = false

[smtp]
enabled = true
host = smtp.mtdt.com:587
user = <EMAIL>
password = <smtp-password>
```

### 4.2 日志管理

#### 4.2.1 ELK配置
```yaml
# /etc/logstash/conf.d/mtdt.conf
input {
  filebeat {
    port => 5044
    host => "0.0.0.0"
  }
}

filter {
  if [type] == "frontend" {
    grok {
      match => { "message" => "%{COMBINEDAPACHELOG}" }
    }
  }
  
  if [type] == "backend" {
    json {
      source => "message"
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "mtdt-%{+YYYY.MM.dd}"
  }
}
```

#### 4.2.2 Filebeat配置
```yaml
# /etc/filebeat/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/logs/nginx/*.log
  fields:
    type: frontend

- type: log
  enabled: true
  paths:
    - /data/logs/api/*.log
  fields:
    type: backend

output.logstash:
  hosts: ["logstash:5044"]
```

## 5. 备份策略

### 5.1 数据备份

#### 5.1.1 数据库备份
```bash
#!/bin/bash
# 数据库备份脚本

# 配置
BACKUP_DIR="/data/backups/db"
RETENTION_DAYS=7
DB_NAME="mtdt"
DB_USER="postgres"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
FILENAME="${DB_NAME}_$(date +%Y%m%d_%H%M%S).dump"
pg_dump -U $DB_USER -Fc $DB_NAME > "$BACKUP_DIR/$FILENAME"

# 压缩备份
gzip "$BACKUP_DIR/$FILENAME"

# 删除旧备份
find $BACKUP_DIR -type f -mtime +$RETENTION_DAYS -delete
```

#### 5.1.2 文件备份
```bash
#!/bin/bash
# 文件备份脚本

# 配置
BACKUP_DIR="/data/backups/files"
RETENTION_DAYS=7
SOURCE_DIRS=("/data/uploads" "/data/configs")

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
FILENAME="files_$(date +%Y%m%d_%H%M%S).tar.gz"
tar czf "$BACKUP_DIR/$FILENAME" ${SOURCE_DIRS[@]}

# 删除旧备份
find $BACKUP_DIR -type f -mtime +$RETENTION_DAYS -delete
```

### 5.2 系统备份

#### 5.2.1 配置备份
```bash
#!/bin/bash
# 配置备份脚本

# 配置
BACKUP_DIR="/data/backups/configs"
RETENTION_DAYS=30
CONFIG_DIRS=("/etc/nginx" "/etc/postgresql" "/etc/redis")

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
FILENAME="configs_$(date +%Y%m%d_%H%M%S).tar.gz"
tar czf "$BACKUP_DIR/$FILENAME" ${CONFIG_DIRS[@]}

# 删除旧备份
find $BACKUP_DIR -type f -mtime +$RETENTION_DAYS -delete
```

## 6. 部署流程

### 6.1 初始部署

1. **环境准备**
   ```bash
   # 更新系统
   apt update && apt upgrade -y
   
   # 安装基础工具
   apt install -y curl wget git vim
   
   # 安装Docker
   curl -fsSL https://get.docker.com | bash
   ```

2. **安装Docker Compose**
   ```bash
   curl -L "https://github.com/docker/compose/releases/download/v2.5.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   chmod +x /usr/local/bin/docker-compose
   ```

3. **克隆代码**
   ```bash
   git clone https://github.com/your-org/mtdt.git
   cd mtdt
   ```

4. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑.env文件设置环境变量
   ```

5. **启动服务**
   ```bash
   docker-compose up -d
   ```

### 6.2 更新部署

1. **拉取最新代码**
   ```bash
   git pull origin main
   ```

2. **构建新镜像**
   ```bash
   docker-compose build
   ```

3. **更新服务**
   ```bash
   docker-compose up -d --no-deps --build <service-name>
   ```

4. **验证部署**
   ```bash
   docker-compose ps
   docker-compose logs -f
   ```

### 6.3 回滚流程

1. **确定回滚版本**
   ```bash
   git log --oneline
   git checkout <commit-hash>
   ```

2. **停止当前服务**
   ```bash
   docker-compose down
   ```

3. **启动旧版本**
   ```bash
   docker-compose up -d
   ```

4. **验证回滚**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 检查日志
   docker-compose logs -f
   ```

## 7. 故障处理

### 7.1 常见问题

#### 7.1.1 服务无响应
```bash
# 检查服务状态
docker-compose ps

# 检查容器日志
docker-compose logs -f <service-name>

# 检查系统资源
top
df -h
free -m
```

#### 7.1.2 数据库问题
```bash
# 检查数据库连接
pg_isready -h localhost -p 5432

# 检查数据库日志
tail -f /var/log/postgresql/postgresql-13-main.log

# 检查数据库进程
ps aux | grep postgres
```

### 7.2 性能优化

#### 7.2.1 系统优化
```bash
# 系统缓存清理
sync; echo 3 > /proc/sys/vm/drop_caches

# 优化系统参数
sysctl -w net.core.somaxconn=65535
sysctl -w net.ipv4.tcp_max_syn_backlog=65535
```

#### 7.2.2 应用优化
```nginx
# Nginx优化配置
worker_processes auto;
worker_rlimit_nofile 65535;
events {
    worker_connections 65535;
    use epoll;
    multi_accept on;
}
```

## 8. 维护计划

### 8.1 定期维护

#### 8.1.1 日常维护
```bash
# 检查系统状态
#!/bin/bash

echo "=== 系统状态检查 ==="
date

echo "--- 磁盘使用情况 ---"
df -h

echo "--- 内存使用情况 ---"
free -m

echo "--- 负载情况 ---"
uptime

echo "--- 服务状态 ---"
docker-compose ps
```

#### 8.1.2 周期维护
```bash
# 周维护任务
0 0 * * 0 /scripts/weekly-maintenance.sh

# 月维护任务
0 0 1 * * /scripts/monthly-maintenance.sh
```

### 8.2 更新维护

#### 8.2.1 系统更新
```bash
# 系统更新脚本
#!/bin/bash

# 更新包列表
apt update

# 安装更新
apt upgrade -y

# 清理旧包
apt autoremove -y
apt clean

# 检查是否需要重启
if [ -f /var/run/reboot-required ]; then
    echo "系统需要重启"
fi
```

#### 8.2.2 应用更新
```bash
# 应用更新脚本
#!/bin/bash

# 拉取最新代码
git pull origin main

# 构建新镜像
docker-compose build

# 更新服务
docker-compose up -d --no-deps --build

# 清理旧镜像
docker image prune -f
```

## 9. 总结

本部署指南提供了山思数字平台的完整部署方案，包括：

1. **环境准备**
   - 系统配置
   - 依赖安装
   - 网络设置

2. **应用部署**
   - 前端部署
   - 后端部署
   - 数据库部署

3. **运维管理**
   - 监控配置
   - 备份策略
   - 维护计划

4. **故障处理**
   - 问题诊断
   - 性能优化
   - 应急预案

建议定期审查和更新本指南，确保部署流程的有效性和适用性。