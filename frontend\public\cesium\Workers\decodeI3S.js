/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.129
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as mt}from"./chunk-5P2J4BSU.js";import{a as P}from"./chunk-HJ6M6NKU.js";import{a as ht}from"./chunk-3WQQGQIW.js";import{a as ft}from"./chunk-R6YCVUGT.js";import{a as g,b as ot,d as st,e as it}from"./chunk-I4JBCTLR.js";import{a as M}from"./chunk-MCEXFPZL.js";import{b as lt}from"./chunk-BOXFFUY5.js";import{d as pt,e as h}from"./chunk-OVZZEY7C.js";var at=pt(mt(),1);function xt(n){return lt.defined("value",n),n<=.04045?n*.07739938080495357:Math.pow((n+.055)*.9478672985781991,2.4)}var k=xt;var ut;function wt(n,t,e,o,a,i){let r=e*(1-n)+o*n,s=a*(1-n)+i*n;return r*(1-t)+s*t}function H(n,t,e,o){let a=n+t*e;return o[a]}function It(n,t,e){let o=e.nativeExtent,a=(n-o.west)/(o.east-o.west)*(e.width-1),i=(t-o.south)/(o.north-o.south)*(e.height-1),r=Math.floor(a),s=Math.floor(i);a-=r,i-=s;let c=r<e.width?r+1:r,u=s<e.height?s+1:s;s=e.height-1-s,u=e.height-1-u;let l=H(r,s,e.width,e.buffer),f=H(c,s,e.width,e.buffer),p=H(r,u,e.width,e.buffer),m=H(c,u,e.width,e.buffer),d=wt(a,i,l,f,p,m);return d=d*e.scale+e.offset,d}function yt(n,t,e){for(let o=0;o<e.length;o++){let a=e[o].nativeExtent,i=new g;if(e[o].projectionType==="WebMercator"){let r=e[o].projection._ellipsoid._radii;i=new ft(new st(r.x,r.y,r.z)).project(new ot(n,t,0))}else i.x=n,i.y=t;if(i.x>a.west&&i.x<a.east&&i.y>a.south&&i.y<a.north)return It(i.x,i.y,e[o])}return 0}function At(n,t,e,o,a,i,r){if(r)return;let s=yt(a.longitude,a.latitude,i);for(let c=0;c<n;++c){let u=yt(a.longitude+M.toRadians(e*t[c*3]),a.latitude+M.toRadians(o*t[c*3+1]),i);t[c*3+2]+=u-s}}function Lt(n,t,e,o,a,i,r,s,c){if(n===0||!h(t)||t.length===0)return;let u=new st(Math.sqrt(r.x),Math.sqrt(r.y),Math.sqrt(r.z));for(let l=0;l<n;++l){let f=l*3,p=f+1,m=f+2,d=new ot;d.longitude=o.longitude+M.toRadians(s*t[f]),d.latitude=o.latitude+M.toRadians(c*t[p]),d.height=o.height+t[m];let b={};u.cartographicToCartesian(d,b),b.x-=a.x,b.y-=a.y,b.z-=a.z;let x={};if(it.multiplyByVector(i,b,x),t[f]=x.x,t[p]=x.y,t[m]=x.z,h(e)){let T=new g(e[f],e[p],e[m]),I={};it.multiplyByVector(i,T,I),e[f]=I.x,e[p]=I.y,e[m]=I.z}}}function Ot(n,t,e){for(let o=0;o<n;++o){let a=e[o*4]/65535,i=e[o*4+1]/65535,r=(e[o*4+2]-e[o*4])/65535,s=(e[o*4+3]-e[o*4+1])/65535;t[o*2]*=r,t[o*2]+=a,t[o*2+1]*=s,t[o*2+1]+=i}}function Tt(n,t,e,o){let a=new Uint32Array(n),i=h(t)?s=>t[s]:s=>s,r=0;if(o&&h(e)){let s=c=>e[i(c)*4+3]<255;for(let c=0;c<n;c+=3)!s(c)&&!s(c+1)&&!s(c+2)&&(a[r++]=i(c),a[r++]=i(c+1),a[r++]=i(c+2));if(r>0){let c=r;for(let u=0;u<n;u+=3)(s(u)||s(u+1)||s(u+2))&&(a[c++]=i(u),a[c++]=i(u+1),a[c++]=i(u+2))}else for(let c=0;c<n;++c)a[c]=i(c)}else{r=n;for(let s=0;s<n;++s)a[s]=i(s)}return{indexArray:a,transparentVertexOffset:r}}function Ct(n,t,e){let o=t[e];if(h(o))return o;let a=t[e]={positions:{},indices:{},edges:{}},i=n[e]??n.default;return a.hasOutline=h(i?.edges),a}function Ft(n,t,e,o){if(!h(n[e])){let a=e*3,i=t;for(let r=0;r<3;r++){let s=o[a+r];h(i[s])||(i[s]={}),i=i[s]}h(i.index)||(i.index=e),n[e]=i.index}}function Ut(n,t,e,o,a,i){let r,s;o<a?(r=o,s=a):(r=a,s=o);let c=n[r];h(c)||(c=n[r]={});let u=c[s];h(u)||(u=c[s]={normalsIndex:[],outlines:[]}),u.normalsIndex.push(i),(u.outlines.length===0||t!==o||e!==a)&&u.outlines.push(t,e)}function Vt(n,t,e,o){let a=[];for(let i=0;i<e.length;i+=3){let r=h(t)?t[e[i]]:"default",s=Ct(n,a,r);if(!s.hasOutline)continue;let c=s.indices,u=s.positions;for(let f=0;f<3;f++){let p=e[i+f];Ft(c,u,p,o)}let l=s.edges;for(let f=0;f<3;f++){let p=e[i+f],m=e[i+(f+1)%3],d=c[p],b=c[m];Ut(l,p,m,d,b,i)}}return a}var O=new g,X=new g,Y=new g;function bt(n,t,e,o){let a=e[t]*3,i=e[t+1]*3,r=e[t+2]*3;g.fromArray(o,a,O),g.fromArray(o,i,X),g.fromArray(o,r,Y),g.subtract(X,O,X),g.subtract(Y,O,Y),g.cross(X,Y,O);let s=g.magnitude(O);s!==0&&g.divideByScalar(O,s,O);let c=t*3,u=(t+1)*3,l=(t+2)*3;g.pack(O,n,c),g.pack(O,n,u),g.pack(O,n,l)}var z=new g,ct=new g;function Et(n,t,e){g.fromArray(n,t,z),g.fromArray(n,e,ct);let o=g.dot(z,ct),a=g.magnitude(g.cross(z,ct,z));return Math.atan2(a,o)<.25}function _t(n,t,e,o,a){if(t.normalsIndex.length>1){let i=o.length===a.length;for(let r=0;r<t.normalsIndex.length;r++){let s=t.normalsIndex[r];if(h(a[s*3])||bt(a,s,e,o),r!==0)for(let c=0;c<r;c++){let u=t.normalsIndex[c],l=i?e[s]*3:s*3,f=i?e[u]*3:u*3;if(Et(a,l,f))return}}}n.push(...t.outlines)}function Rt(n,t,e,o,a){let i=Object.keys(t);for(let r=0;r<i.length;r++){let s=t[i[r]],c=Object.keys(s);for(let u=0;u<c.length;u++){let l=s[c[u]];_t(n,l,e,o,a)}}}function Nt(n,t,e,o){let a=[],i=Object.keys(n);for(let r=0;r<i.length;r++){let s=n[i[r]].edges;Rt(a,s,t,e,o)}return a}function Bt(n,t,e,o,a){if(!h(n)||Object.keys(n).length===0)return;let i=Vt(n,t,e,o);(!h(a)||e.length*3!==a.length)&&(a=[]);let r=Nt(i,e,o,a);return r.length>0?new Uint32Array(r):void 0}function Gt(n){let t=new Float32Array(n.length);for(let e=0;e<n.length;e+=4)t[e]=k(P.byteToFloat(n[e])),t[e+1]=k(P.byteToFloat(n[e+1])),t[e+2]=k(P.byteToFloat(n[e+2])),t[e+3]=P.byteToFloat(n[e+3]);return t}function St(n,t,e,o,a,i,r){let s={normals:void 0,positions:void 0,uv0s:void 0,colors:void 0,featureIndex:void 0,vertexCount:void 0};if(n===0||!h(e)||e.length===0||h(o))return s;if(h(t)){s.vertexCount=t.length,s.positions=new Float32Array(t.length*3),s.uv0s=h(a)?new Float32Array(t.length*2):void 0,s.colors=h(i)?new Uint8Array(t.length*4):void 0,s.featureIndex=h(r)?new Array(t.length):void 0;for(let c=0;c<t.length;c++){let u=t[c];s.positions[c*3]=e[u*3],s.positions[c*3+1]=e[u*3+1],s.positions[c*3+2]=e[u*3+2],h(s.uv0s)&&(s.uv0s[c*2]=a[u*2],s.uv0s[c*2+1]=a[u*2+1]),h(s.colors)&&(s.colors[c*4]=i[u*4],s.colors[c*4+1]=i[u*4+1],s.colors[c*4+2]=i[u*4+2],s.colors[c*4+3]=i[u*4+3]),h(s.featureIndex)&&(s.featureIndex[c]=r[u])}n=t.length,e=s.positions}t=new Array(n);for(let c=0;c<n;c++)t[c]=c;s.normals=new Float32Array(t.length*3);for(let c=0;c<t.length;c+=3)bt(s.normals,c,t,e);return s}function vt(n,t,e,o,a,i,r,s){if(n===0||!h(e)||e.length===0)return{buffers:[],bufferViews:[],accessors:[],meshes:[],nodes:[],nodesInScene:[]};let c=[],u=[],l=[],f=[],p=[],m=[],d={},b=[];h(t)&&(n=t.length);let{indexArray:x,transparentVertexOffset:T}=Tt(n,t,i,s.splitGeometryByColorTransparency),I=new Blob([x],{type:"application/binary"}),A=URL.createObjectURL(I),w=n,F=s.enableFeatures&&h(r)?new Float32Array(r.length):void 0,C=0;if(h(F))for(let y=0;y<r.length;++y){F[y]=r[y];let E=r[y]+1;C<E&&(C=E)}let U,R=Bt(s.symbologyData,r,x,e,o);if(h(R)){let y=new Blob([R],{type:"application/binary"});U=URL.createObjectURL(y)}let L=e.subarray(0,w*3),gt=new Blob([L],{type:"application/binary"}),dt=URL.createObjectURL(gt),D=Number.POSITIVE_INFINITY,K=Number.NEGATIVE_INFINITY,Q=Number.POSITIVE_INFINITY,Z=Number.NEGATIVE_INFINITY,J=Number.POSITIVE_INFINITY,$=Number.NEGATIVE_INFINITY;for(let y=0;y<L.length/3;y++)D=Math.min(D,L[y*3+0]),K=Math.max(K,L[y*3+0]),Q=Math.min(Q,L[y*3+1]),Z=Math.max(Z,L[y*3+1]),J=Math.min(J,L[y*3+2]),$=Math.max($,L[y*3+2]);let N=o?o.subarray(0,w*3):void 0,q;if(h(N)){let y=new Blob([N],{type:"application/binary"});q=URL.createObjectURL(y)}let B=a?a.subarray(0,w*2):void 0,tt;if(h(B)){let y=new Blob([B],{type:"application/binary"});tt=URL.createObjectURL(y)}let G=h(i)?Gt(i.subarray(0,w*4)):void 0,et;if(h(G)){let y=new Blob([G],{type:"application/binary"});et=URL.createObjectURL(y)}let S=h(F)?F.subarray(0,w):void 0,nt;if(h(S)){let y=new Blob([S],{type:"application/binary"});nt=URL.createObjectURL(y)}let _=h(F)?new Float32Array(C):void 0,rt;if(h(_)){for(let E=0;E<_.length;++E)_[E]=E;let y=new Blob([_],{type:"application/binary"});rt=URL.createObjectURL(y)}let V={},j={};V.POSITION=l.length,c.push({uri:dt,byteLength:L.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:L.byteLength,target:34962}),l.push({bufferView:u.length-1,byteOffset:0,componentType:5126,count:L.length/3,type:"VEC3",max:[D,Q,J],min:[K,Z,$]}),h(q)&&(V.NORMAL=l.length,c.push({uri:q,byteLength:N.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:N.byteLength,target:34962}),l.push({bufferView:u.length-1,byteOffset:0,componentType:5126,count:N.length/3,type:"VEC3"})),h(tt)&&(V.TEXCOORD_0=l.length,c.push({uri:tt,byteLength:B.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:B.byteLength,target:34962}),l.push({bufferView:u.length-1,byteOffset:0,componentType:5126,count:B.length/2,type:"VEC2"})),h(et)&&(V.COLOR_0=l.length,c.push({uri:et,byteLength:G.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:G.byteLength,target:34962}),l.push({bufferView:u.length-1,byteOffset:0,componentType:5126,count:G.length/4,type:"VEC4"})),h(nt)&&(V._FEATURE_ID_0=l.length,c.push({uri:nt,byteLength:S.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:S.byteLength,target:34963}),l.push({bufferView:u.length-1,byteOffset:0,componentType:5126,count:S.length,type:"SCALAR"}),j.EXT_mesh_features={featureIds:[{attribute:0,propertyTable:0,featureCount:C}]},b.push("EXT_mesh_features")),h(rt)&&(c.push({uri:rt,byteLength:_.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:_.byteLength,target:34963}),d.EXT_structural_metadata={schema:{id:"i3s-metadata-schema-001",name:"I3S metadata schema 001",description:"The schema for I3S metadata",version:"1.0",classes:{feature:{name:"feature",description:"Feature metadata",properties:{index:{description:"The feature index",type:"SCALAR",componentType:"FLOAT32",required:!0}}}}},propertyTables:[{name:"feature-indices-mapping",class:"feature",count:C,properties:{index:{values:u.length-1}}}]},b.push("EXT_structural_metadata")),h(U)&&(c.push({uri:U,byteLength:R.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:R.byteLength,target:34963}),l.push({bufferView:u.length-1,byteOffset:0,componentType:5125,count:R.length,type:"SCALAR"}),j.CESIUM_primitive_outline={indices:l.length-1},b.push("CESIUM_primitive_outline")),c.push({uri:A,byteLength:x.byteLength}),u.push({buffer:c.length-1,byteOffset:0,byteLength:x.byteLength,target:34963});let v=[];return T>0&&(l.push({bufferView:u.length-1,byteOffset:0,componentType:5125,count:T,type:"SCALAR"}),v.push({attributes:V,indices:l.length-1,material:v.length,extensions:j})),T<n&&(l.push({bufferView:u.length-1,byteOffset:4*T,componentType:5125,count:n-T,type:"SCALAR"}),v.push({attributes:V,indices:l.length-1,material:v.length,extensions:j,extra:{isTransparent:!0}})),f.push({primitives:v}),m.push(0),p.push({mesh:0}),{buffers:c,bufferViews:u,accessors:l,meshes:f,nodes:p,nodesInScene:m,rootExtensions:d,extensionsUsed:b}}function Mt(n,t,e,o){let a=new Uint8Array(n,0,5);return a[0]===68&&a[1]===82&&a[2]===65&&a[3]===67&&a[4]===79?Pt(n,e):kt(n,t,e,o)}function Pt(n){let t=ut,e=new t.DecoderBuffer,o=new Uint8Array(n);e.Init(o,o.length);let a=new t.Decoder,i=a.GetEncodedGeometryType(e),r=new t.MetadataQuerier,s,c;i===t.TRIANGULAR_MESH&&(s=new t.Mesh,c=a.DecodeBufferToMesh(e,s));let u={vertexCount:[0],featureCount:0};if(h(c)&&c.ok()&&s.ptr!==0){let l=s.num_faces(),f=s.num_attributes(),p=s.num_points();u.indices=new Uint32Array(l*3);let m=u.indices;u.vertexCount[0]=p,u.scale_x=1,u.scale_y=1;let d=new t.DracoInt32Array(3);for(let b=0;b<l;++b)a.GetFaceFromMesh(s,b,d),m[b*3]=d.GetValue(0),m[b*3+1]=d.GetValue(1),m[b*3+2]=d.GetValue(2);t.destroy(d);for(let b=0;b<f;++b){let x=a.GetAttribute(s,b),T=jt(t,a,s,x,p),I=x.attribute_type(),A="unknown";I===t.POSITION?A="positions":I===t.NORMAL?A="normals":I===t.COLOR?A="colors":I===t.TEX_COORD&&(A="uv0s");let w=a.GetAttributeMetadata(s,b);if(w.ptr!==0){let F=r.NumEntries(w);for(let C=0;C<F;++C){let U=r.GetEntryName(w,C);U==="i3s-scale_x"?u.scale_x=r.GetDoubleEntry(w,"i3s-scale_x"):U==="i3s-scale_y"?u.scale_y=r.GetDoubleEntry(w,"i3s-scale_y"):U==="i3s-attribute-type"&&(A=r.GetStringEntry(w,"i3s-attribute-type"))}}h(u[A])&&console.log("Attribute already exists",A),u[A]=T,A==="feature-index"&&u.featureCount++}t.destroy(s)}return t.destroy(r),t.destroy(a),u}function jt(n,t,e,o,a){let i=o.num_components()*a,r,c=[function(){},function(){r=new n.DracoInt8Array(i),t.GetAttributeInt8ForAllPoints(e,o,r)||console.error("Bad stream");let l=new Int8Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l},function(){r=new n.DracoInt8Array(i),t.GetAttributeUInt8ForAllPoints(e,o,r)||console.error("Bad stream");let l=new Uint8Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l},function(){r=new n.DracoInt16Array(i),t.GetAttributeInt16ForAllPoints(e,o,r)||console.error("Bad stream");let l=new Int16Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l},function(){r=new n.DracoInt16Array(i),t.GetAttributeUInt16ForAllPoints(e,o,r)||console.error("Bad stream");let l=new Uint16Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l},function(){r=new n.DracoInt32Array(i),t.GetAttributeInt32ForAllPoints(e,o,r)||console.error("Bad stream");let l=new Int32Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l},function(){r=new n.DracoInt32Array(i),t.GetAttributeUInt32ForAllPoints(e,o,r)||console.error("Bad stream");let l=new Uint32Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l},function(){},function(){},function(){r=new n.DracoFloat32Array(i),t.GetAttributeFloatForAllPoints(e,o,r)||console.error("Bad stream");let l=new Float32Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l},function(){},function(){r=new n.DracoUInt8Array(i),t.GetAttributeUInt8ForAllPoints(e,o,r)||console.error("Bad stream");let l=new Uint8Array(i);for(let f=0;f<i;++f)l[f]=r.GetValue(f);return l}][o.data_type()]();return h(r)&&n.destroy(r),c}var W={position:function(n,t,e){let o=n.vertexCount*3;return n.positions=new Float32Array(t,e,o),e+=o*4,e},normal:function(n,t,e){let o=n.vertexCount*3;return n.normals=new Float32Array(t,e,o),e+=o*4,e},uv0:function(n,t,e){let o=n.vertexCount*2;return n.uv0s=new Float32Array(t,e,o),e+=o*4,e},color:function(n,t,e){let o=n.vertexCount*4;return n.colors=new Uint8Array(t,e,o),e+=o,e},featureId:function(n,t,e){let o=n.featureCount;return e+=o*8,e},id:function(n,t,e){let o=n.featureCount;return e+=o*8,e},faceRange:function(n,t,e){let o=n.featureCount*2;return n.faceRange=new Uint32Array(t,e,o),e+=o*4,e},uvRegion:function(n,t,e){let o=n.vertexCount*4;return n["uv-region"]=new Uint16Array(t,e,o),e+=o*2,e},region:function(n,t,e){let o=n.vertexCount*4;return n["uv-region"]=new Uint16Array(t,e,o),e+=o*2,e}};function kt(n,t,e,o){let a={vertexCount:0},i=new DataView(n);try{let r=0;if(a.vertexCount=i.getUint32(r,1),r+=4,a.featureCount=i.getUint32(r,1),r+=4,h(e))for(let s=0;s<e.attributes.length;s++)h(W[e.attributes[s]])?r=W[e.attributes[s]](a,n,r):console.error("Unknown decoder for",e.attributes[s]);else{let s=t.ordering,c=t.featureAttributeOrder;h(o)&&h(o.geometryData)&&h(o.geometryData[0])&&h(o.geometryData[0].params)&&(s=Object.keys(o.geometryData[0].params.vertexAttributes),c=Object.keys(o.geometryData[0].params.featureAttributes));for(let u=0;u<s.length;u++){let l=W[s[u]];r=l(a,n,r)}for(let u=0;u<c.length;u++){let l=W[c[u]];r=l(a,n,r)}}}catch(r){console.error(r)}return a.scale_x=1,a.scale_y=1,a}function Ht(n){let t=Mt(n.binaryData,n.schema,n.bufferInfo,n.featureData);h(n.geoidDataList)&&n.geoidDataList.length>0&&At(t.vertexCount,t.positions,t.scale_x,t.scale_y,n.cartographicCenter,n.geoidDataList,!1),Lt(t.vertexCount,t.positions,t.normals,n.cartographicCenter,n.cartesianCenter,n.parentRotation,n.ellipsoidRadiiSquare,t.scale_x,t.scale_y),h(t.uv0s)&&h(t["uv-region"])&&Ot(t.vertexCount,t.uv0s,t["uv-region"]);let e;if(h(t["feature-index"]))e=t["feature-index"];else if(h(t.faceRange)){e=new Array(t.vertexCount);for(let r=0;r<t.faceRange.length-1;r+=2){let s=r/2,c=t.faceRange[r],u=t.faceRange[r+1];for(let l=c;l<=u;l++)e[l*3]=s,e[l*3+1]=s,e[l*3+2]=s}}if(n.calculateNormals){let r=St(t.vertexCount,t.indices,t.positions,t.normals,t.uv0s,t.colors,e);h(r.normals)&&(t.normals=r.normals,h(r.vertexCount)&&(t.vertexCount=r.vertexCount,t.indices=r.indices,t.positions=r.positions,t.uv0s=r.uv0s,t.colors=r.colors,e=r.featureIndex))}let o=vt(t.vertexCount,t.indices,t.positions,t.normals,t.uv0s,t.colors,e,n),a={positions:t.positions,indices:t.indices,featureIndex:e,sourceURL:n.url,cartesianCenter:n.cartesianCenter,parentRotation:n.parentRotation};return o._customAttributes=a,{meshData:o}}async function Xt(n,t){let e=n.webAssemblyConfig;return h(e)&&h(e.wasmBinaryFile)?ut=await(0,at.default)(e):ut=await(0,at.default)(),!0}function Yt(n,t){let e=n.webAssemblyConfig;return h(e)?Xt(n,t):Ht(n,t)}var re=ht(Yt);export{re as default};
