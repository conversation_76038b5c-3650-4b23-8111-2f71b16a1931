const { Client } = require('pg');
const bcrypt = require('bcryptjs');
require('dotenv').config();

async function createAdminUser() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'mtdt_db',
  });

  try {
    await client.connect();
    console.log('✅ 连接到数据库成功');

    // 1. 创建管理员角色
    console.log('🔧 创建管理员角色...');
    const adminRoleId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'; // 固定的UUID
    await client.query(`
      INSERT INTO system.roles (role_id, name, display_name, description, permissions, is_system)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (name) DO NOTHING
    `, [
      adminRoleId,
      'admin',
      '系统管理员',
      '系统管理员，拥有所有权限',
      JSON.stringify(['*']), // 所有权限
      true
    ]);

    // 2. 创建普通用户角色
    console.log('🔧 创建普通用户角色...');
    const userRoleId = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a22'; // 固定的UUID
    await client.query(`
      INSERT INTO system.roles (role_id, name, display_name, description, permissions, is_system)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (name) DO NOTHING
    `, [
      userRoleId,
      'user',
      '普通用户',
      '普通用户，基本权限',
      JSON.stringify(['read']),
      true
    ]);

    // 3. 创建管理员用户
    console.log('👤 创建管理员用户...');
    const passwordHash = await bcrypt.hash('11111111', 10);
    const adminUserId = 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a33'; // 固定的UUID
    
    await client.query(`
      INSERT INTO system.users (
        user_id, username, email, password_hash, full_name, 
        is_active, is_first_login, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      ON CONFLICT (username) DO NOTHING
    `, [
      adminUserId,
      'lhgadmin',
      '<EMAIL>',
      passwordHash,
      '系统管理员',
      true,
      false // 不是首次登录
    ]);

    // 4. 为管理员用户分配管理员角色
    console.log('🔗 分配管理员角色...');
    await client.query(`
      INSERT INTO system.user_roles (user_id, role_id)
      VALUES ($1, $2)
      ON CONFLICT (user_id, role_id) DO NOTHING
    `, [adminUserId, adminRoleId]);

    console.log('🎉 管理员用户创建完成！');
    console.log('📋 登录信息:');
    console.log('   用户名: lhgadmin');
    console.log('   密码: 11111111');
    console.log('   邮箱: <EMAIL>');

    // 验证创建结果
    const userCheck = await client.query(`
      SELECT u.username, u.email, u.is_active, r.name as role_name
      FROM system.users u
      LEFT JOIN system.user_roles ur ON u.user_id = ur.user_id
      LEFT JOIN system.roles r ON ur.role_id = r.role_id
      WHERE u.username = 'lhgadmin'
    `);

    if (userCheck.rows.length > 0) {
      console.log('\n✅ 验证成功，用户已创建:');
      userCheck.rows.forEach(row => {
        console.log(`   ${row.username} (${row.email}) - 角色: ${row.role_name || '无'}`);
      });
    }

  } catch (error) {
    console.error('❌ 创建管理员用户失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await client.end();
  }
}

createAdminUser();
