const http = require('http');

function testLogin() {
  console.log('🔐 测试登录 API...');

  const postData = JSON.stringify({
    username: 'lhgadmin',
    password: '11111111'
  });

  const options = {
    hostname: '127.0.0.1',
    port: 3000,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`📊 响应状态: ${res.statusCode} ${res.statusMessage}`);

    let responseData = '';
    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      console.log('📄 响应内容:', responseData);

      if (res.statusCode === 200 || res.statusCode === 201) {
        try {
          const data = JSON.parse(responseData);
          console.log('✅ 登录成功！');
          console.log('🎫 访问令牌:', data.access_token ? '已获取' : '未获取');
          console.log('🔄 刷新令牌:', data.refresh_token ? '已获取' : '未获取');
        } catch (e) {
          console.log('❌ 解析响应失败:', e.message);
        }
      } else {
        console.log('❌ 登录失败');
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ 请求失败:', error.message);
  });

  req.write(postData);
  req.end();
}

testLogin();
