{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.2417707443237305, "root": {"boundingVolume": {"box": [9997.521484375, 2236.968994140625, -499.3313903808594, 177.37109375, 0.0, 0.0, 0.0, 125.499267578125, 0.0, 0.0, 0.0, 120.13314819335938]}, "children": [{"boundingVolume": {"box": [9894.0546875, 2236.968994140625, -499.20269775390625, 73.90478515625, 0.0, 0.0, 0.0, 125.499267578125, 0.0, 0.0, 0.0, 118.12493896484375]}, "children": [{"boundingVolume": {"box": [9894.0546875, 2174.21923828125, -522.1026000976562, 73.90478515625, 0.0, 0.0, 0.0, 62.7496337890625, 0.0, 0.0, 0.0, 95.0078125]}, "children": [{"boundingVolume": {"box": [9894.0546875, 2166.98193359375, -567.4024658203125, 73.90478515625, 0.0, 0.0, 0.0, 55.5120849609375, 0.0, 0.0, 0.0, 46.330413818359375]}, "content": {"uri": "BlockA_L19_385.b3dm"}, "geometricError": 0.17644613981246948, "refine": "REPLACE"}, {"boundingVolume": {"box": [9894.0546875, 2178.390380859375, -474.7416687011719, 73.90478515625, 0.0, 0.0, 0.0, 58.57861328125, 0.0, 0.0, 0.0, 46.330413818359375]}, "content": {"uri": "BlockA_L19_384.b3dm"}, "geometricError": 0.15832512080669403, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2028.b3dm"}, "geometricError": 0.33637407422065735, "refine": "REPLACE"}, {"boundingVolume": {"box": [9894.0546875, 2299.71875, -443.6966247558594, 73.90478515625, 0.0, 0.0, 0.0, 62.7496337890625, 0.0, 0.0, 0.0, 62.618865966796875]}, "children": [{"boundingVolume": {"box": [9857.1025390625, 2299.71875, -443.6966247558594, 36.9521484375, 0.0, 0.0, 0.0, 62.7496337890625, 0.0, 0.0, 0.0, 62.618865966796875]}, "content": {"uri": "BlockA_L19_383.b3dm"}, "geometricError": 0.14271153509616852, "refine": "REPLACE"}, {"boundingVolume": {"box": [9931.0078125, 2299.71875, -437.02874755859375, 36.95263671875, 0.0, 0.0, 0.0, 62.7496337890625, 0.0, 0.0, 0.0, 48.41581726074219]}, "content": {"uri": "BlockA_L19_382.b3dm"}, "geometricError": 0.14003606140613556, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2027.b3dm"}, "geometricError": 0.2828434407711029, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1049.b3dm"}, "geometricError": 0.6183278560638428, "refine": "REPLACE"}, {"boundingVolume": {"box": [10071.42578125, 2236.968994140625, -513.95263671875, 103.46630859375, 0.0, 0.0, 0.0, 125.499267578125, 0.0, 0.0, 0.0, 105.51187133789062]}, "children": [{"boundingVolume": {"box": [10071.42578125, 2163.76123046875, -511.06658935546875, 103.46630859375, 0.0, 0.0, 0.0, 52.2913818359375, 0.0, 0.0, 0.0, 88.60603332519531]}, "children": [{"boundingVolume": {"box": [10011.0703125, 2152.44189453125, -499.9344482421875, 43.11083984375, 0.0, 0.0, 0.0, 40.9722900390625, 0.0, 0.0, 0.0, 31.892471313476562]}, "content": {"uri": "BlockA_L19_381.b3dm"}, "geometricError": 0.1573881059885025, "refine": "REPLACE"}, {"boundingVolume": {"box": [10011.0703125, 2171.31640625, -445.2615966796875, 43.11083984375, 0.0, 0.0, 0.0, 44.7362060546875, 0.0, 0.0, 0.0, 22.780349731445312]}, "content": {"uri": "BlockA_L19_380.b3dm"}, "geometricError": 0.14632746577262878, "refine": "REPLACE"}, {"boundingVolume": {"box": [10114.537109375, 2163.76123046875, -519.4306640625, 60.35546875, 0.0, 0.0, 0.0, 52.2913818359375, 0.0, 0.0, 0.0, 77.78742980957031]}, "content": {"uri": "BlockA_L19_379.b3dm"}, "geometricError": 0.1589178591966629, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2026.b3dm"}, "geometricError": 0.30729129910469055, "refine": "REPLACE"}, {"boundingVolume": {"box": [10071.42578125, 2289.26025390625, -513.9371337890625, 103.46630859375, 0.0, 0.0, 0.0, 73.2078857421875, 0.0, 0.0, 0.0, 105.49636840820312]}, "children": [{"boundingVolume": {"box": [10085.18359375, 2289.26025390625, -557.500732421875, 89.70947265625, 0.0, 0.0, 0.0, 73.2078857421875, 0.0, 0.0, 0.0, 61.30555725097656]}, "content": {"uri": "BlockA_L19_378.b3dm"}, "geometricError": 0.1740252673625946, "refine": "REPLACE"}, {"boundingVolume": {"box": [10034.5673828125, 2289.26025390625, -452.40545654296875, 66.607421875, 0.0, 0.0, 0.0, 73.2078857421875, 0.0, 0.0, 0.0, 43.78968811035156]}, "content": {"uri": "BlockA_L19_377.b3dm"}, "geometricError": 0.14631229639053345, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2025.b3dm"}, "geometricError": 0.3140300214290619, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1048.b3dm"}, "geometricError": 0.6219106316566467, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_527.b3dm"}, "geometricError": 1.2417707443237305, "refine": "REPLACE"}}