import {
  Injectable,
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Res,
  HttpCode,
  HttpStatus,
  Get,
  UnauthorizedException,
  Logger,
  Param,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthService, LoginResponse, RefreshTokenResponse } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { Public } from './decorators/public.decorator';
import { GetUser } from './decorators/get-user.decorator';
import { User } from '../entities/user.entity';
import { LoginDto, ChangePasswordDto, RefreshTokenDto, RegisterDto, SendEmailCodeDto, EmailLoginDto } from './dto/login.dto';

@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  /**
   * 用户登录
   */
  @Public()
  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(
    @Body() loginDto: LoginDto,
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
  ): Promise<{
    message: string;
    data: LoginResponse;
  }> {
    this.logger.log(`=== LOGIN REQUEST RECEIVED === Username: ${loginDto.username}`);
    try {
      const userAgent = req.get('user-agent');
      const ipAddress = this.getClientIpAddress(req);

      const loginResult = await this.authService.login(loginDto, userAgent, ipAddress);

      // 设置刷新令牌到 HttpOnly Cookie
      res.cookie('refreshToken', loginResult.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      this.logger.log(`User ${loginDto.username} logged in successfully`);

      return {
        message: '登录成功',
        data: loginResult,
      };
    } catch (error) {
      this.logger.error(`Login failed for user ${loginDto.username}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 刷新访问令牌
   */
  @Public()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Req() req: Request,
  ): Promise<{
    message: string;
    data: RefreshTokenResponse;
  }> {
    try {
      // 优先使用请求体中的刷新令牌，如果没有则使用 Cookie
      const refreshToken = refreshTokenDto.refreshToken || req.cookies?.refreshToken;

      if (!refreshToken) {
        throw new UnauthorizedException('刷新令牌缺失');
      }

      const result = await this.authService.refreshToken(refreshToken);

      return {
        message: '令牌刷新成功',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  async logout(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @GetUser() user: User,
  ): Promise<{
    message: string;
  }> {
    try {
      const refreshToken = req.cookies?.refreshToken;

      if (refreshToken) {
        await this.authService.logout(refreshToken);
      }

      // 清除刷新令牌 Cookie
      res.clearCookie('refreshToken');

      this.logger.log(`User ${user.username} logged out successfully`);

      return {
        message: '登出成功',
      };
    } catch (error) {
      this.logger.error(`Logout failed for user ${user.username}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 登出所有设备
   */
  @UseGuards(JwtAuthGuard)
  @Post('logout-all')
  @HttpCode(HttpStatus.OK)
  async logoutAllDevices(
    @GetUser() user: User,
    @Res({ passthrough: true }) res: Response,
  ): Promise<{
    message: string;
  }> {
    try {
      await this.authService.logoutAllDevices(user.id);

      // 清除当前设备的刷新令牌 Cookie
      res.clearCookie('refreshToken');

      this.logger.log(`User ${user.username} logged out from all devices`);

      return {
        message: '已从所有设备登出',
      };
    } catch (error) {
      this.logger.error(`Logout all devices failed for user ${user.username}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 修改密码
   */
  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @GetUser() user: User,
    @Res({ passthrough: true }) res: Response,
  ): Promise<{
    message: string;
  }> {
    try {
      await this.authService.changePassword(user.id, changePasswordDto);

      // 清除刷新令牌 Cookie（强制重新登录）
      res.clearCookie('refreshToken');

      this.logger.log(`Password changed for user ${user.username}`);

      return {
        message: '密码修改成功，请重新登录',
      };
    } catch (error) {
      this.logger.error(`Password change failed for user ${user.username}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   */
  @UseGuards(JwtAuthGuard)
  @Get('me')
  async getCurrentUser(
    @GetUser() user: User,
  ): Promise<{
    message: string;
    data: {
      id: string;
      username: string;
      email: string;
      fullName: string;
      roles: string[];
      permissions: string[];
      isActive: boolean;
      lastLoginAt?: Date;
      createdAt: Date;
    };
  }> {
    const permissions = this.getUserPermissions(user);

    return {
      message: '获取用户信息成功',
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        roles: user.roles.map(role => role.name),
        permissions,
        isActive: user.isActive,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
      },
    };
  }

  /**
   * 验证令牌有效性
   */
  @UseGuards(JwtAuthGuard)
  @Post('verify')
  @HttpCode(HttpStatus.OK)
  async verifyToken(
    @GetUser() user: User,
  ): Promise<{
    message: string;
    data: {
      valid: boolean;
      user: {
        id: string;
        username: string;
        roles: string[];
      };
    };
  }> {
    return {
      message: '令牌验证成功',
      data: {
        valid: true,
        user: {
          id: user.id,
          username: user.username,
          roles: user.roles.map(role => role.name),
        },
      },
    };
  }

  /**
   * 重置用户密码（临时调试端点）
   */
  @Public()
  @Post('reset-password/:username')
  async resetPassword(
    @Param('username') username: string,
    @Body() body: { password: string },
  ) {
    try {
      // 使用认证服务重置密码
      const result = await this.authService.resetPassword(username, body.password);
      
      return {
        message: '密码重置成功',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException(`密码重置失败: ${error.message}`);
    }
  }

  /**
   * 用户注册
   */
  @Public()
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async register(
    @Body() registerDto: RegisterDto,
  ): Promise<{
    message: string;
    data: {
      id: string;
      username: string;
      email: string;
      fullName: string;
    };
  }> {
    try {
      const result = await this.authService.register(registerDto);

      this.logger.log(`User ${registerDto.username} registered successfully`);

      return {
        message: result.message,
        data: result.user,
      };
    } catch (error) {
      this.logger.error(`Registration failed for user ${registerDto.username}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送邮箱验证码
   */
  @Public()
  @Post('send-email-code')
  @HttpCode(HttpStatus.OK)
  async sendEmailCode(
    @Body() sendEmailCodeDto: SendEmailCodeDto,
  ): Promise<{
    message: string;
  }> {
    try {
      const { email, type } = sendEmailCodeDto;

      try {
        const result = await this.authService.sendEmailCode(email, type);
        this.logger.log(`Email verification code sent to: ${email}`);
        return {
          message: result.message,
        };
      } catch (mailError) {
        // 如果是邮件发送失败，但验证码已生成，返回成功
        if (mailError.message && mailError.message.includes('Authentication failure')) {
          this.logger.log(`Mail sending failed but verification code generated for: ${email}`);
          return {
            message: '验证码已生成，请查看后台日志获取验证码',
          };
        }
        throw mailError;
      }
    } catch (error) {
      this.logger.error(`Failed to send email code to ${sendEmailCodeDto.email}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 邮箱验证码登录
   */
  @Public()
  @Post('login-email')
  @HttpCode(HttpStatus.OK)
  async loginWithEmail(
    @Body() emailLoginDto: EmailLoginDto,
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
  ): Promise<{
    message: string;
    data: LoginResponse;
  }> {
    try {
      const userAgent = req.get('user-agent');
      const ipAddress = this.getClientIpAddress(req);

      const loginResult = await this.authService.loginWithEmail(emailLoginDto);

      // 设置刷新令牌到 HttpOnly Cookie
      res.cookie('refreshToken', loginResult.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      this.logger.log(`User logged in with email: ${emailLoginDto.email}`);

      return {
        message: '邮箱验证码登录成功',
        data: loginResult,
      };
    } catch (error) {
      this.logger.error(`Email login failed for ${emailLoginDto.email}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取客户端 IP 地址
   */
  private getClientIpAddress(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (req.headers['x-real-ip'] as string) ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      req.ip ||
      'unknown'
    );
  }

  /**
   * 获取用户权限
   */
  private getUserPermissions(user: User): string[] {
    const permissions = new Set<string>();
    
    if (user.roles && Array.isArray(user.roles)) {
      user.roles.forEach(role => {
        const rolePermissions = role.getPermissions();
        rolePermissions.forEach(permission => {
          permissions.add(permission);
        });
      });
    }

    return Array.from(permissions);
  }
}