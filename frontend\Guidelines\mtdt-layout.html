<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<script src="https://cdn.tailwindcss.com"></script>
<link href="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
<script src="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Cesium.js"></script>
<script>
tailwind.config = {
theme: {
extend: {
colors: {
primary: '#1d4ed8',
secondary: '#475569'
},
borderRadius: {
'none': '0px',
'sm': '2px',
DEFAULT: '4px',
'md': '8px',
'lg': '12px',
'xl': '16px',
'2xl': '20px',
'3xl': '24px',
'full': '9999px',
'button': '4px'
}
}
}
}
</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<style>
body {
min-height: 1024px;
overflow: hidden;
}
.tool-button {
width: 36px;
height: 36px;
display: flex;
justify-content: center;
align-items: center;
background: rgba(255, 255, 255, 0.9);
transition: all 0.3s;
cursor: pointer;
margin-bottom: 8px;
box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.tool-button:hover {
background: #1d4ed8;
color: white;
}
@keyframes scroll {
0% { transform: translateX(100%); }
100% { transform: translateX(-100%); }
}
.animate-scroll {
animation: scroll 15s linear infinite;
}
</style>
</head>
<body>
<div class="w-full h-full bg-white">
<header class="h-14 bg-white border-b border-gray-200/50 flex items-center px-4 justify-between">
<div class="font-['Pacifico'] text-xl text-primary">logo</div>
<div class="flex items-center gap-4 absolute left-1/2 -translate-x-1/2 bg-white/80">
<div class="font-['Pacifico'] text-xl text-primary">logo</div>
<h1 class="text-lg font-medium">山思数字管理平台V0.1</h1>
</div>
<div class="overflow-hidden w-80">
<div class="animate-scroll flex items-center gap-6 text-sm whitespace-nowrap">
<div class="flex items-center gap-2">
<i class="fas fa-temperature-high text-red-500"></i>
<span>温度: 26℃</span>
</div>
<div class="flex items-center gap-2">
<i class="fas fa-droplet text-blue-500"></i>
<span>湿度: 65%</span>
</div>
<div class="flex items-center gap-2">
<i class="fas fa-cloud-rain text-gray-500"></i>
<span>降水: 0mm</span>
</div>
<div class="flex items-center gap-2">
<i class="fas fa-wind text-gray-500"></i>
<span>风速: 3m/s</span>
</div>
</div>
</div>
</header>
<main class="h-[calc(100vh-56px)] overflow-hidden relative">
<div class="absolute left-4 top-4 z-10 flex flex-col">
<button class="tool-button !rounded-button group relative" title="测量工具">
<i class="fas fa-ruler"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">测量工具</span>
</button>
<button class="tool-button !rounded-button group relative" title="绘图工具">
<i class="fas fa-draw-polygon"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">绘图工具</span>
</button>
<button class="tool-button !rounded-button group relative" title="地图管理">
<i class="fas fa-map"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">地图管理</span>
</button>
<button class="tool-button !rounded-button group relative" title="图层管理">
<i class="fas fa-layer-group"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">图层管理</span>
</button>
<button class="tool-button !rounded-button group relative" title="标记位置">
<i class="fas fa-map-marker-alt"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">标记位置</span>
</button>
<button class="tool-button !rounded-button group relative" title="无人机控制">
<i class="fas fa-helicopter"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">无人机控制</span>
</button>
<button class="tool-button !rounded-button group relative" title="视频监控">
<i class="fas fa-video"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">视频监控</span>
</button>
<button class="tool-button !rounded-button group relative" title="AI分析">
<i class="fas fa-robot"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">AI分析</span>
</button>
<button class="tool-button !rounded-button group relative" title="空间分析">
<i class="fas fa-cube"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">空间分析</span>
</button>
<button class="tool-button !rounded-button group relative" title="地理编辑">
<i class="fas fa-earth-americas"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">地理编辑</span>
</button>
<button class="tool-button !rounded-button group relative" title="工作查询">
<i class="fas fa-database"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">工作查询</span>
</button>
<button class="tool-button !rounded-button group relative" title="人员位置">
<i class="fas fa-street-view"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">人员位置</span>
</button>
<button class="tool-button !rounded-button group relative" title="清除标记">
<i class="fas fa-eraser"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">清除标记</span>
</button>
<button class="tool-button !rounded-button group relative" title="退出系统">
<i class="fas fa-sign-out-alt"></i>
<span class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 whitespace-nowrap">退出系统</span>
</button>
</div>
<div class="absolute right-4 bottom-4 flex flex-col gap-2 z-10">
<button class="tool-button !rounded-button" title="放大">
<i class="fas fa-plus"></i>
</button>
<button class="tool-button !rounded-button" title="缩小">
<i class="fas fa-minus"></i>
</button>
<button class="tool-button !rounded-button" title="当前位置">
<i class="fas fa-location-dot"></i>
</button>
<button class="tool-button !rounded-button" title="Home">
<i class="fas fa-home"></i>
</button>
</div>
<div class="w-full h-full" id="cesiumContainer"></div>
<script>
Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.OZUnpry8gYo_JI2PWDiTxZN-IwrRXonVFiP0RMXPHxs';
const viewer = new Cesium.Viewer('cesiumContainer', {
terrainProvider: Cesium.createWorldTerrain(),
animation: false,
baseLayerPicker: false,
fullscreenButton: false,
geocoder: false,
homeButton: false,
infoBox: false,
sceneModePicker: false,
selectionIndicator: false,
timeline: false,
navigationHelpButton: false,
navigationInstructionsInitiallyVisible: false
});
viewer.scene.globe.enableLighting = true;
</script>
</main>
</div>
</body>
</html>
