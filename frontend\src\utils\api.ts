interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

class ApiClient {
  private baseURL: string

  constructor(baseURL = "/api") {
    this.baseURL = baseURL
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`
      
      // 获取访问令牌并添加到请求头
      const token = localStorage.getItem('access_token')
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
        ...options.headers as Record<string, string>,
      }
      
      if (token) {
        headers.Authorization = `Bearer ${token}`
      }
      
      const response = await fetch(url, {
        headers,
        credentials: 'include', // 确保携带Cookie（用于刷新令牌）
        ...options,
      })

      if (!response.ok) {
        // 如果是401未授权错误，可能需要刷新令牌
        if (response.status === 401) {
          console.warn('Access token may be expired, consider refreshing')
          // 这里可以触发令牌刷新逻辑
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return {
        success: true,
        data,
      }
    } catch (error) {
      console.error("API request failed:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET" })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" })
  }
}

export const apiClient = new ApiClient()
export const api = apiClient // 提供简短的别名

// Wildlife API endpoints
export const wildlifeAPI = {
  getSpecies: () => apiClient.get('/wildlife/species'),
  getObservations: () => apiClient.get('/wildlife/observations'),
  createObservation: (data: any) => apiClient.post('/wildlife/observations', data),
  updateObservation: (id: string, data: any) => apiClient.put(`/wildlife/observations/${id}`, data),
  deleteObservation: (id: string) => apiClient.delete(`/wildlife/observations/${id}`)
}

// Export both named and default exports
export { ApiClient }
export default ApiClient
