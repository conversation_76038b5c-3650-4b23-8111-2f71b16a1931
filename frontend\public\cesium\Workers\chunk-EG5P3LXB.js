/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.129
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as L}from"./chunk-PWIRITCZ.js";import{a as S}from"./chunk-HNJRTQHB.js";import{a as D}from"./chunk-I4PVU2XE.js";import{a as H}from"./chunk-TMRGWDA2.js";import{b as T,c as B,d as x}from"./chunk-OST65WKL.js";import{d as E}from"./chunk-KLPRJ6SC.js";import{a as O}from"./chunk-J4RA3VLE.js";import{a as c,d,f as V}from"./chunk-I4JBCTLR.js";import{a as w}from"./chunk-MCEXFPZL.js";import{a as A}from"./chunk-BOXFFUY5.js";import{e as a}from"./chunk-OVZZEY7C.js";var q=new c,g=new c;function U(e){let t=e.center;g=c.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(t,g),e.height,g),g=c.add(t,g,g);let i=new E(g,e.semiMajorAxis),f=L.computeEllipsePositions(e,!1,!0).outerPositions,o=new H({position:new x({componentDatatype:O.DOUBLE,componentsPerAttribute:3,values:L.raisePositionsToHeight(f,e,!1)})}),r=f.length/3,u=D.createTypedArray(r,r*2),m=0;for(let n=0;n<r;++n)u[m++]=n,u[m++]=(n+1)%r;return{boundingSphere:i,attributes:o,indices:u}}var j=new E,N=new E;function R(e){let t=e.center,i=e.ellipsoid,f=e.semiMajorAxis,o=c.multiplyByScalar(i.geodeticSurfaceNormal(t,q),e.height,q);j.center=c.add(t,o,j.center),j.radius=f,o=c.multiplyByScalar(i.geodeticSurfaceNormal(t,o),e.extrudedHeight,o),N.center=c.add(t,o,N.center),N.radius=f;let r=L.computeEllipsePositions(e,!1,!0).outerPositions,u=new H({position:new x({componentDatatype:O.DOUBLE,componentsPerAttribute:3,values:L.raisePositionsToHeight(r,e,!0)})});r=u.position.values;let m=E.union(j,N),n=r.length/3;if(a(e.offsetAttribute)){let b=new Uint8Array(n);if(e.offsetAttribute===S.TOP)b=b.fill(1,0,n/2);else{let k=e.offsetAttribute===S.NONE?0:1;b=b.fill(k)}u.applyOffset=new x({componentDatatype:O.UNSIGNED_BYTE,componentsPerAttribute:1,values:b})}let l=e.numberOfVerticalLines??16;l=w.clamp(l,0,n/2);let h=D.createTypedArray(n,n*2+l*2);n/=2;let p=0,s;for(s=0;s<n;++s)h[p++]=s,h[p++]=(s+1)%n,h[p++]=s+n,h[p++]=(s+1)%n+n;let P;if(l>0){let b=Math.min(l,n);P=Math.round(n/b);let k=Math.min(P*l,n);for(s=0;s<k;s+=P)h[p++]=s,h[p++]=s+n}return{boundingSphere:m,attributes:u,indices:h}}function M(e){e=e??V.EMPTY_OBJECT;let t=e.center,i=e.ellipsoid??d.default,f=e.semiMajorAxis,o=e.semiMinorAxis,r=e.granularity??w.RADIANS_PER_DEGREE;if(!a(t))throw new A("center is required.");if(!a(f))throw new A("semiMajorAxis is required.");if(!a(o))throw new A("semiMinorAxis is required.");if(f<o)throw new A("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(r<=0)throw new A("granularity must be greater than zero.");let u=e.height??0,m=e.extrudedHeight??u;this._center=c.clone(t),this._semiMajorAxis=f,this._semiMinorAxis=o,this._ellipsoid=d.clone(i),this._rotation=e.rotation??0,this._height=Math.max(m,u),this._granularity=r,this._extrudedHeight=Math.min(m,u),this._numberOfVerticalLines=Math.max(e.numberOfVerticalLines??16,0),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipseOutlineGeometry"}M.packedLength=c.packedLength+d.packedLength+8;M.pack=function(e,t,i){if(!a(e))throw new A("value is required");if(!a(t))throw new A("array is required");return i=i??0,c.pack(e._center,t,i),i+=c.packedLength,d.pack(e._ellipsoid,t,i),i+=d.packedLength,t[i++]=e._semiMajorAxis,t[i++]=e._semiMinorAxis,t[i++]=e._rotation,t[i++]=e._height,t[i++]=e._granularity,t[i++]=e._extrudedHeight,t[i++]=e._numberOfVerticalLines,t[i]=e._offsetAttribute??-1,t};var y=new c,C=new d,_={center:y,ellipsoid:C,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};M.unpack=function(e,t,i){if(!a(e))throw new A("array is required");t=t??0;let f=c.unpack(e,t,y);t+=c.packedLength;let o=d.unpack(e,t,C);t+=d.packedLength;let r=e[t++],u=e[t++],m=e[t++],n=e[t++],l=e[t++],h=e[t++],p=e[t++],s=e[t];return a(i)?(i._center=c.clone(f,i._center),i._ellipsoid=d.clone(o,i._ellipsoid),i._semiMajorAxis=r,i._semiMinorAxis=u,i._rotation=m,i._height=n,i._granularity=l,i._extrudedHeight=h,i._numberOfVerticalLines=p,i._offsetAttribute=s===-1?void 0:s,i):(_.height=n,_.extrudedHeight=h,_.granularity=l,_.rotation=m,_.semiMajorAxis=r,_.semiMinorAxis=u,_.numberOfVerticalLines=p,_.offsetAttribute=s===-1?void 0:s,new M(_))};M.createGeometry=function(e){if(e._semiMajorAxis<=0||e._semiMinorAxis<=0)return;let t=e._height,i=e._extrudedHeight,f=!w.equalsEpsilon(t,i,0,w.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);let o={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:t,granularity:e._granularity,numberOfVerticalLines:e._numberOfVerticalLines},r;if(f)o.extrudedHeight=i,o.offsetAttribute=e._offsetAttribute,r=R(o);else if(r=U(o),a(e._offsetAttribute)){let u=r.attributes.position.values.length,m=e._offsetAttribute===S.NONE?0:1,n=new Uint8Array(u/3).fill(m);r.attributes.applyOffset=new x({componentDatatype:O.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new B({attributes:r.attributes,indices:r.indices,primitiveType:T.LINES,boundingSphere:r.boundingSphere,offsetAttribute:e._offsetAttribute})};var ie=M;export{ie as a};
