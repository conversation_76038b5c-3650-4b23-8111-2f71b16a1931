{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.252962350845337, "root": {"boundingVolume": {"box": [9535.703125, 4424.275390625, -499.45550537109375, 149.599609375, 0.0, 0.0, 0.0, 89.675048828125, 0.0, 0.0, 0.0, 111.31913757324219]}, "children": [{"boundingVolume": {"box": [9460.9033203125, 4424.275390625, -499.17572021484375, 74.7998046875, 0.0, 0.0, 0.0, 89.675048828125, 0.0, 0.0, 0.0, 111.03932189941406]}, "children": [{"boundingVolume": {"box": [9460.9033203125, 4424.275390625, -545.427734375, 74.7998046875, 0.0, 0.0, 0.0, 89.675048828125, 0.0, 0.0, 0.0, 64.59059143066406]}, "children": [{"boundingVolume": {"box": [9460.9033203125, 4379.4375, -545.427734375, 74.7998046875, 0.0, 0.0, 0.0, 44.837646484375, 0.0, 0.0, 0.0, 64.59059143066406]}, "content": {"uri": "BlockA_L19_245.b3dm"}, "geometricError": 0.1649002730846405, "refine": "REPLACE"}, {"boundingVolume": {"box": [9460.9033203125, 4469.11279296875, -513.4159545898438, 74.7998046875, 0.0, 0.0, 0.0, 44.83740234375, 0.0, 0.0, 0.0, 32.57884216308594]}, "content": {"uri": "BlockA_L19_244.b3dm"}, "geometricError": 0.16192878782749176, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1960.b3dm"}, "geometricError": 0.3268314599990845, "refine": "REPLACE"}, {"boundingVolume": {"box": [9460.9033203125, 4424.275390625, -434.6001892089844, 74.7998046875, 0.0, 0.0, 0.0, 89.675048828125, 0.0, 0.0, 0.0, 46.2369384765625]}, "children": [{"boundingVolume": {"box": [9460.9033203125, 4386.91064453125, -451.70330810546875, 74.7998046875, 0.0, 0.0, 0.0, 52.310546875, 0.0, 0.0, 0.0, 29.133804321289062]}, "content": {"uri": "BlockA_L19_243.b3dm"}, "geometricError": 0.15004035830497742, "refine": "REPLACE"}, {"boundingVolume": {"box": [9450.0361328125, 4476.5859375, -450.381591796875, 28.5556640625, 0.0, 0.0, 0.0, 37.364501953125, 0.0, 0.0, 0.0, 30.455551147460938]}, "content": {"uri": "BlockA_L19_242.b3dm"}, "geometricError": 0.14966201782226562, "refine": "REPLACE"}, {"boundingVolume": {"box": [9507.1474609375, 4476.5859375, -429.21282958984375, 28.5556640625, 0.0, 0.0, 0.0, 37.364501953125, 0.0, 0.0, 0.0, 40.64796447753906]}, "content": {"uri": "BlockA_L19_241.b3dm"}, "geometricError": 0.13987180590629578, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1959.b3dm"}, "geometricError": 0.29360198974609375, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1015.b3dm"}, "geometricError": 0.6131025552749634, "refine": "REPLACE"}, {"boundingVolume": {"box": [9610.5029296875, 4424.275390625, -502.42425537109375, 74.7998046875, 0.0, 0.0, 0.0, 89.675048828125, 0.0, 0.0, 0.0, 107.79078674316406]}, "children": [{"boundingVolume": {"box": [9610.5029296875, 4409.1669921875, -556.157958984375, 74.7998046875, 0.0, 0.0, 0.0, 74.566650390625, 0.0, 0.0, 0.0, 53.752166748046875]}, "children": [{"boundingVolume": {"box": [9573.103515625, 4409.1669921875, -555.94140625, 37.39990234375, 0.0, 0.0, 0.0, 74.566650390625, 0.0, 0.0, 0.0, 53.535675048828125]}, "content": {"uri": "BlockA_L19_240.b3dm"}, "geometricError": 0.17405670881271362, "refine": "REPLACE"}, {"boundingVolume": {"box": [9647.90234375, 4406.7099609375, -536.22802734375, 37.39990234375, 0.0, 0.0, 0.0, 72.10986328125, 0.0, 0.0, 0.0, 33.822296142578125]}, "content": {"uri": "BlockA_L19_239.b3dm"}, "geometricError": 0.1673871874809265, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1958.b3dm"}, "geometricError": 0.3422415852546692, "refine": "REPLACE"}, {"boundingVolume": {"box": [9610.5029296875, 4438.431640625, -448.7225646972656, 74.7998046875, 0.0, 0.0, 0.0, 75.518798828125, 0.0, 0.0, 0.0, 53.683197021484375]}, "children": [{"boundingVolume": {"box": [9610.5029296875, 4407.671875, -472.3623046875, 74.7998046875, 0.0, 0.0, 0.0, 43.76171875, 0.0, 0.0, 0.0, 30.043441772460938]}, "content": {"uri": "BlockA_L19_238.b3dm"}, "geometricError": 0.15531200170516968, "refine": "REPLACE"}, {"boundingVolume": {"box": [9610.5029296875, 4482.69189453125, -448.87005615234375, 74.7998046875, 0.0, 0.0, 0.0, 31.25830078125, 0.0, 0.0, 0.0, 53.53569030761719]}, "content": {"uri": "BlockA_L19_237.b3dm"}, "geometricError": 0.14882467687129974, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1957.b3dm"}, "geometricError": 0.30307403206825256, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1014.b3dm"}, "geometricError": 0.6418997645378113, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_510.b3dm"}, "geometricError": 1.252962350845337, "refine": "REPLACE"}}