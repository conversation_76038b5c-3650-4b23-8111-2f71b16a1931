const { Client } = require('pg');
require('dotenv').config();

async function initDatabase() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'mtdt_db',
  });

  try {
    await client.connect();
    console.log('✅ 连接到数据库成功');

    // 创建 system schema
    await client.query('CREATE SCHEMA IF NOT EXISTS system;');
    console.log('✅ system schema 创建成功');

    // 创建 uuid-ossp 扩展（如果需要）
    await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
    console.log('✅ uuid-ossp 扩展创建成功');

    console.log('🎉 数据库初始化完成！');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

initDatabase();
