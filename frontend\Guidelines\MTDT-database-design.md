# 山思数字平台数据库设计文档

## 1. 数据库概述

### 1.1 数据库选型

- 主数据库：PostgreSQL 17 (Docker容器)
- 空间扩展：PostGIS 3.3+ (预留，当前未启用)
- 缓存数据库：Redis 7 (Docker容器)
- 容器化部署：Docker + Docker Compose

### 1.2 设计原则

1. **空间数据优先**
   - 使用PostGIS空间类型
   - 优化空间索引
   - 考虑空间查询性能

2. **性能优化**
   - 合理的表结构设计
   - 适当的索引策略
   - 分区表应用

3. **可扩展性**
   - 预留扩展字段
   - 版本控制支持
   - 分库分表准备

## 2. 数据库架构

### 2.1 数据库实例

```mermaid
graph TD
    A[主库 Master] --> B[从库 Slave 1]
    A --> C[从库 Slave 2]
    D[Redis Cluster] --> E[缓存节点 1]
    D --> F[缓存节点 2]
    D --> G[缓存节点 3]
```

### 2.2 当前数据库模式

```sql
-- 当前使用的模式
CREATE SCHEMA system;  -- 系统管理相关表

-- 预留模式（未来扩展）
-- CREATE SCHEMA spatial;   -- 空间数据相关表
-- CREATE SCHEMA business;  -- 业务数据相关表
```

## 3. 当前表设计

### 3.1 系统管理表（已实现）

#### 3.1.1 用户表（system.users）
```sql
CREATE TABLE system.users (
    user_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    username varchar(50) NOT NULL,
    email varchar(255) NOT NULL,
    password_hash varchar(255) NOT NULL,
    full_name varchar(100),
    is_active boolean DEFAULT true,
    last_login timestamptz,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT user_email_unique UNIQUE (email),
    CONSTRAINT user_username_unique UNIQUE (username)
);

-- 添加索引
CREATE INDEX idx_users_email ON system.users (email);
CREATE INDEX idx_users_username ON system.users (username);
```

#### 3.1.2 角色表（system.roles）
```sql
CREATE TABLE system.roles (
    role_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name varchar(50) NOT NULL,
    description text,
    permissions jsonb,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT role_name_unique UNIQUE (name)
);

-- 添加索引
CREATE INDEX idx_roles_name ON system.roles (name);
```

#### 3.1.3 用户角色关联表（system.user_roles）
```sql
CREATE TABLE system.user_roles (
    user_id uuid REFERENCES system.users(user_id),
    role_id uuid REFERENCES system.roles(role_id),
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

-- 添加索引
CREATE INDEX idx_user_roles_user_id ON system.user_roles (user_id);
CREATE INDEX idx_user_roles_role_id ON system.user_roles (role_id);
```

#### 3.1.4 用户会话表（system.user_sessions）
```sql
CREATE TABLE system.user_sessions (
    session_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES system.users(user_id),
    token_hash varchar(255) NOT NULL,
    ip_address inet,
    user_agent text,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    expires_at timestamptz NOT NULL,
    is_active boolean DEFAULT true
);

-- 添加索引
CREATE INDEX idx_user_sessions_user_id ON system.user_sessions (user_id);
CREATE INDEX idx_user_sessions_token ON system.user_sessions (token_hash);
CREATE INDEX idx_user_sessions_expires ON system.user_sessions (expires_at);
```

#### 3.1.5 审计日志表（system.audit_logs）
```sql
CREATE TABLE system.audit_logs (
    log_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES system.users(user_id),
    action varchar(100) NOT NULL,
    resource_type varchar(50),
    resource_id uuid,
    details jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引
CREATE INDEX idx_audit_logs_user_id ON system.audit_logs (user_id);
CREATE INDEX idx_audit_logs_action ON system.audit_logs (action);
CREATE INDEX idx_audit_logs_created_at ON system.audit_logs (created_at DESC);
CREATE INDEX idx_audit_logs_details ON system.audit_logs USING GIN (details);
```

### 3.3 系统管理表

#### 3.3.1 用户表（system.users）
```sql
CREATE TABLE system.users (
    user_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    username varchar(50) NOT NULL,
    email varchar(255) NOT NULL,
    password_hash varchar(255) NOT NULL,
    full_name varchar(100),
    is_active boolean DEFAULT true,
    last_login timestamptz,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT user_email_unique UNIQUE (email),
    CONSTRAINT user_username_unique UNIQUE (username)
);
```

#### 3.3.2 角色表（system.roles）
```sql
CREATE TABLE system.roles (
    role_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name varchar(50) NOT NULL,
    description text,
    permissions jsonb,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT role_name_unique UNIQUE (name)
);
```

#### 3.3.3 用户角色关联表（system.user_roles）
```sql
CREATE TABLE system.user_roles (
    user_id uuid REFERENCES system.users(user_id),
    role_id uuid REFERENCES system.roles(role_id),
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);
```

## 4. 索引策略

### 4.1 空间索引
```sql
-- 图层边界索引
CREATE INDEX idx_layers_boundary ON spatial.layers USING GIST (boundary);

-- 要素几何索引
CREATE INDEX idx_features_geometry ON spatial.features USING GIST (geometry);

-- 项目边界索引
CREATE INDEX idx_projects_boundary ON business.projects USING GIST (boundary);
```

### 4.2 业务索引
```sql
-- 用户查询优化
CREATE INDEX idx_users_username ON system.users (username);
CREATE INDEX idx_users_email ON system.users (email);

-- 图层查询优化
CREATE INDEX idx_layers_type ON spatial.layers (type);
CREATE INDEX idx_layers_active ON spatial.layers (is_active);

-- 时间查询优化
CREATE INDEX idx_features_created ON spatial.features (created_at);
```

## 5. 数据库分区

### 5.1 要素表分区
```sql
-- 按时间分区的要素表
CREATE TABLE spatial.features_partitioned (
    feature_id uuid,
    layer_id uuid,
    geometry geometry(Geometry, 4326),
    properties jsonb,
    created_at timestamptz,
    updated_at timestamptz,
    created_by uuid,
    version int
) PARTITION BY RANGE (created_at);

-- 创建月度分区
CREATE TABLE spatial.features_y2024m01 PARTITION OF spatial.features_partitioned
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 5.2 无人机数据分区
```sql
-- 按时间分区的无人机数据表
CREATE TABLE business.drone_data_partitioned (
    data_id uuid,
    drone_id uuid,
    position geometry(Point, 4326),
    altitude double precision,
    speed double precision,
    heading double precision,
    battery_level int,
    status varchar(50),
    telemetry jsonb,
    timestamp timestamptz
) PARTITION BY RANGE (timestamp);

-- 创建日分区
CREATE TABLE business.drone_data_y2024m01d01 PARTITION OF business.drone_data_partitioned
    FOR VALUES FROM ('2024-01-01') TO ('2024-01-02');
```

## 6. 数据库维护

### 6.1 备份策略
```bash
# 每日全量备份
pg_dump -Fc -f /backup/full_$(date +%Y%m%d).dump

# 时间点恢复配置
wal_level = replica
archive_mode = on
archive_command = 'cp %p /archive/%f'
```

### 6.2 性能优化
```sql
-- 自动清理配置
ALTER SYSTEM SET autovacuum_vacuum_scale_factor = 0.1;
ALTER SYSTEM SET autovacuum_analyze_scale_factor = 0.05;

-- 共享缓冲区配置
ALTER SYSTEM SET shared_buffers = '4GB';

-- 工作内存配置
ALTER SYSTEM SET work_mem = '16MB';
```

### 6.3 监控指标
```sql
-- 表大小监控
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename))
FROM pg_tables
WHERE schemaname IN ('spatial', 'business', 'system')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes;
```

## 7. 数据迁移

### 7.1 版本控制
```sql
-- 版本控制表
CREATE TABLE system.schema_versions (
    version_id serial PRIMARY KEY,
    version varchar(50) NOT NULL,
    description text,
    applied_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    script_name varchar(255),
    checksum varchar(64)
);
```

### 7.2 迁移脚本示例
```sql
-- V1__initial_schema.sql
BEGIN;

-- 创建初始架构
CREATE SCHEMA IF NOT EXISTS spatial;
CREATE SCHEMA IF NOT EXISTS business;
CREATE SCHEMA IF NOT EXISTS system;

-- 创建基础表
CREATE TABLE spatial.layers ( ... );
CREATE TABLE spatial.features ( ... );

COMMIT;
```

## 8. 安全策略

### 8.1 角色权限
```sql
-- 创建应用角色
CREATE ROLE app_user;
CREATE ROLE app_admin;

-- 授权
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA spatial TO app_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA spatial TO app_admin;
```

### 8.2 行级安全
```sql
-- 启用行级安全
ALTER TABLE spatial.layers ENABLE ROW LEVEL SECURITY;

-- 创建策略
CREATE POLICY layer_access_policy ON spatial.layers
    USING (created_by = current_user_id() OR 
           exists (
               SELECT 1 FROM system.user_roles ur
               WHERE ur.user_id = current_user_id()
               AND ur.role_id IN (SELECT role_id FROM system.roles WHERE name = 'admin')
           ));
```

## 9. 性能优化建议

### 9.1 查询优化
1. **空间查询优化**
   ```sql
   -- 使用ST_DWithin代替ST_Distance
   SELECT * FROM spatial.features
   WHERE ST_DWithin(geometry, ST_SetSRID(ST_Point(120, 30), 4326), 0.01);
   
   -- 使用空间索引
   CREATE INDEX idx_features_geometry ON spatial.features USING GIST (geometry);
   ```

2. **JSON查询优化**
   ```sql
   -- 创建GIN索引
   CREATE INDEX idx_features_properties ON spatial.features USING GIN (properties);
   
   -- 使用jsonb_path_ops
   CREATE INDEX idx_features_properties_path ON spatial.features USING GIN (properties jsonb_path_ops);
   ```

### 9.2 配置优化
```postgresql
# postgresql.conf 优化建议

# 内存配置
shared_buffers = '4GB'                  # 系统内存的25%
effective_cache_size = '12GB'           # 系统内存的75%
maintenance_work_mem = '1GB'            # 用于维护操作的内存

# 并发配置
max_connections = 200                   # 根据应用需求调整
max_worker_processes = 8               # CPU核心数
max_parallel_workers_per_gather = 4    # CPU核心数的一半
max_parallel_workers = 8               # CPU核心数

# 写入优化
wal_buffers = '16MB'                   # shared_buffers的1/32
checkpoint_completion_target = 0.9      # 降低检查点的I/O负载
```

## 10. 总结

本数据库设计文档提供了山思数字平台的数据库完整设计方案，包括：

1. **核心表结构**
   - 空间数据管理
   - 业务数据存储
   - 系统管理功能

2. **优化策略**
   - 合理的索引设计
   - 表分区方案
   - 性能优化配置

3. **维护方案**
   - 备份恢复
   - 监控方案
   - 安全策略

4. **扩展性考虑**
   - 版本控制
   - 分区策略
   - 扩展字段

建议定期审查和更新本文档，确保数据库设计持续满足系统需求。