import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis';
import * as nodemailer from 'nodemailer';

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);
  private transporter: nodemailer.Transporter;
  private redis: Redis;

  constructor(private readonly redisService: RedisService) {
    try {
      // 获取 Redis 连接
      this.redis = this.redisService.getOrThrow();
      this.logger.log('✅ Redis 连接成功');
      
      // 测试Redis连接
      this.testRedisConnection();
    } catch (error) {
      this.logger.error('❌ Redis 连接失败:', error);
      throw error;
    }
    
    // 初始化邮件传输器
    try {
      this.logger.log(`📧 邮件配置信息:`);
      this.logger.log(`   MAIL_HOST: ${process.env.MAIL_HOST}`);
      this.logger.log(`   MAIL_PORT: ${process.env.MAIL_PORT}`);
      this.logger.log(`   MAIL_USER: ${process.env.MAIL_USER}`);
      this.logger.log(`   MAIL_PASS: ${process.env.MAIL_PASS ? '***已设置***' : '未设置'}`);

      // 尝试多种SMTP配置
      const mailHost = process.env.MAIL_HOST || 'smtp.qq.com';
      const mailPort = parseInt(process.env.MAIL_PORT || '587');

      this.transporter = nodemailer.createTransport({
        host: process.env.MAIL_HOST || 'smtp.pfi.org.cn',
        port: parseInt(process.env.MAIL_PORT || '465'),
        secure: true, // 使用SSL加密 (端口465)
        auth: {
          user: process.env.MAIL_USER || '<EMAIL>',
          pass: process.env.MAIL_PASS || 'Welcome@314',
        },
        tls: {
          rejectUnauthorized: false, // 忽略SSL证书验证
        },
        debug: true, // 启用调试
        logger: true, // 启用日志
        connectionTimeout: 60000, // 连接超时
        greetingTimeout: 30000, // 问候超时
        socketTimeout: 60000, // socket超时
      });

      this.logger.log('✅ 邮件服务初始化完成');

      // 测试SMTP连接
      this.testSMTPConnection();
    } catch (error) {
      this.logger.error('❌ 邮件服务初始化失败:', error);
    }
  }

  /**
   * 测试Redis连接
   */
  private async testRedisConnection(): Promise<void> {
    try {
      await this.redis.ping();
      this.logger.log('🔗 Redis ping 测试成功');
    } catch (error) {
      this.logger.error('❌ Redis ping 测试失败:', error);
    }
  }

  /**
   * 测试SMTP连接
   */
  private async testSMTPConnection(): Promise<void> {
    try {
      this.logger.log('🔗 开始测试SMTP连接...');
      await this.transporter.verify();
      this.logger.log('✅ SMTP连接测试成功');
    } catch (error) {
      this.logger.error('❌ SMTP连接测试失败:', error);
    }
  }
  /**
   * 生成6位数字验证码
   */
  private generateVerificationCode(): string {
    return Math.random().toString().slice(2, 8);
  }

  /**
   * 发送邮箱验证码
   */
  async sendVerificationCode(email: string, type: 'login' | 'register' | 'reset' = 'login'): Promise<string> {
    try {
      const code = this.generateVerificationCode();
      const cacheKey = `email_code:${type}:${email}`;
      const rateLimitKey = `${cacheKey}:sent`;
      
      this.logger.log(`🔑 开始发送验证码 - 邮箱: ${email}, 类型: ${type}`);

      // 检查发送频率限制（60秒内只能发送一次）
      try {
        const lastSent = await this.redis.get(rateLimitKey);
        if (lastSent) {
          throw new Error('验证码发送过于频繁，请60秒后再试');
        }
      } catch (redisError) {
        this.logger.warn('Redis频率检查失败:', redisError);
        // 继续执行，不因为Redis失败而中断
      }

      // 存储验证码到Redis，5分钟过期
      try {
        await this.redis.setex(cacheKey, 300, code);
        this.logger.log(`💾 验证码已存储到Redis: ${cacheKey} = ${code}`);
        
        // 设置发送频率限制，60秒
        await this.redis.setex(rateLimitKey, 60, '1');
        this.logger.log(`⏰ 频率限制已设置: ${rateLimitKey}`);
      } catch (redisError) {
        this.logger.error('❌ Redis存储失败:', redisError);
        throw new Error('验证码存储失败，请稍后重试');
      }

      // 发送邮件验证码
      this.logger.log(`🔑 验证码生成成功: ${code}`);
      this.logger.log(`🔍 当前NODE_ENV: ${process.env.NODE_ENV}`);

      // 发送邮件验证码
      this.logger.log(`🔑 验证码生成成功: ${code}`);
      this.logger.log(`🔍 当前NODE_ENV: ${process.env.NODE_ENV}`);

      try {
        const mailOptions = {
          from: `"山思数字平台" <${process.env.MAIL_USER}>`,
          to: email,
          subject: '山思数字平台 - 邮箱验证码',
          html: this.getEmailTemplate(code, type),
        };

        this.logger.log(`📧 开始发送邮件到: ${email}`);
        await this.transporter.sendMail(mailOptions);
        this.logger.log(`✅ 验证码已成功发送到邮箱: ${email}`);
      } catch (mailError) {
        this.logger.error(`❌ 邮件发送失败:`, mailError);
        this.logger.log(`📧 邮件发送失败，但验证码已生成: ${code}`);
        // 不抛出错误，让注册流程继续
      }

      return code;
    } catch (error) {
      this.logger.error(`❌ 发送验证码失败 (${email}):`, error);
      throw error;
    }
  }
  /**
   * 验证邮箱验证码
   */
  async verifyCode(email: string, code: string, type: 'login' | 'register' | 'reset' = 'login'): Promise<boolean> {
    try {
      const cacheKey = `email_code:${type}:${email}`;
      
      this.logger.log(`🔍 开始验证验证码 - 邮箱: ${email}, 输入码: ${code}, 类型: ${type}`);
      this.logger.log(`🔍 Redis查询键: ${cacheKey}`);

      const storedCode = await this.redis.get(cacheKey);
      this.logger.log(`💾 Redis存储的验证码: ${storedCode}`);

      if (!storedCode) {
        this.logger.warn(`❌ 验证码不存在或已过期 - 键: ${cacheKey}`);
        return false;
      }

      if (storedCode === code) {
        this.logger.log(`✅ 验证码验证成功 - 邮箱: ${email}`);
        
        // 验证成功后删除验证码
        try {
          await this.redis.del(cacheKey);
          await this.redis.del(`${cacheKey}:sent`);
          this.logger.log(`🗑️ 验证码已清理`);
        } catch (delError) {
          this.logger.warn('清理验证码失败:', delError);
        }
        
        return true;
      } else {
        this.logger.warn(`❌ 验证码不匹配 - 输入: ${code}, 存储: ${storedCode}`);
        return false;
      }

    } catch (error) {
      this.logger.error(`❌ 验证验证码失败 (${email}):`, error);
      return false;
    }
  }

  /**
   * 邮件模板
   */
  private getEmailTemplate(code: string, type: string): string {
    const typeText = {
      login: '登录',
      register: '注册',
      reset: '重置密码'
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>山思数字平台验证码</title>
      </head>
      <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">🏔️ 山思数字平台</h1>
          <p style="margin: 5px 0 0 0; opacity: 0.9;">Mountain Tech Digital Platform</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
          <h2 style="color: #333; margin-top: 0;">邮箱验证码</h2>
          <p style="color: #666; line-height: 1.6;">
            您正在进行 <strong>${typeText[type]}</strong> 操作，验证码为：
          </p>
          
          <div style="background: white; border: 2px solid #3b82f6; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
            <span style="font-size: 32px; font-weight: bold; color: #1e3a8a; letter-spacing: 5px;">${code}</span>
          </div>
          
          <p style="color: #666; line-height: 1.6;">
            • 验证码有效期为 <strong>5分钟</strong><br>
            • 请勿将验证码告知他人<br>
            • 如非本人操作，请忽略此邮件
          </p>
          
          <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
          
          <p style="color: #999; font-size: 12px; text-align: center; margin: 0;">
            此邮件由系统自动发送，请勿回复<br>
            © 2024 山思数字平台 | Mountain Tech Digital Platform
          </p>
        </div>
      </body>
      </html>
    `;
  }
}