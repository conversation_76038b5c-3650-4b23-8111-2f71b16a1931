# GIS应用图层管理功能使用指南

## 概述

本指南介绍了GIS应用中优化后的图层管理功能，包括重新设计的工具栏按钮组和专业的图层管理面板。

## 功能特性

### 1. 重新设计的工具栏

工具栏现在按功能分组，提供更清晰的用户界面：

#### GIS应用组
- **图层管理** (主要功能) - 打开图层管理面板
- **地图管理** - 地图设置和配置
- **测量工具** - 距离、面积、高度测量
- **绘图工具** - 点、线、面绘制

#### 空间分析组
- **空间分析** - 空间查询和分析
- **地理编辑** - 地理要素编辑
- **AI分析** - 智能分析功能

#### 监控设备组
- **无人机控制** - 无人机管理
- **视频监控** - 监控设备管理

#### 业务管理组
- **保护区域** - 保护区域管理
- **业务管理** - 业务数据管理
- **人员位置** - 人员定位

#### 系统操作组
- **清除标记** - 清除所有标记
- **退出系统** - 安全退出

### 2. 专业的图层管理面板

#### 核心功能
- **图层搜索和筛选** - 快速查找图层
- **图层分组管理** - 按类别组织图层
- **图层统计信息** - 显示总数、可见数、加载中数量
- **图层可见性控制** - 单独控制图层显示/隐藏
- **透明度调节** - 精确控制每个图层的透明度
- **多数据源支持** - 支持本地文件、数据库、网络服务
- **智能样式编辑** - 基于数据特征的样式配置
- **字段标注功能** - 基于属性字段的标注显示

#### 数据加载功能
- **本地文件** - 支持Shapefile、GeoJSON、KML等格式
- **数据库连接** - 支持PostgreSQL、MySQL、SQLite
- **网络服务** - 支持WMS、WFS、ArcGIS MapServer等
- **批量上传** - 支持多文件同时上传

### 3. 简洁的地图控件

地图右下角提供基础地图控制：

- **放大** - 地图放大
- **缩小** - 地图缩小
- **定位** - 定位到当前位置
- **主页** - 返回默认视图

## 使用方法

### 基本操作

1. **打开图层管理**
   - 点击工具栏左侧"GIS应用"组中的图层管理按钮（图层图标）
   - 图层管理面板将从右侧滑出

2. **添加图层**
   - 在图层管理面板点击"+"按钮
   - 选择添加方式：本地文件、数据库、网络服务
   - 填写相关信息并确认

3. **管理图层**
   - 使用搜索框快速查找图层
   - 点击分组名称展开/折叠分组
   - 使用开关控制图层可见性
   - 拖动滑块调节透明度

### 高级功能

1. **智能样式编辑**
   - 选择图层后点击样式编辑按钮
   - 系统自动分析数据特征
   - 提供智能样式建议

2. **字段标注**
   - 选择图层后点击"标注"按钮
   - 配置基于字段的标注显示
   - 自定义标注样式和位置

3. **数据库集成**
   - 配置多个数据库连接
   - 实时加载数据库中的空间数据
   - 支持SQL查询条件

## 技术实现

### 组件结构
```
ToolBar.vue - 重新设计的分组工具栏
├── GIS应用组 (包含图层管理)
├── 感知源管理组
├── AI分析
├── 业务管理
└── 系统操作

LayerPanel.vue - 专业的图层管理面板
├── 图层统计
├── 搜索和筛选
├── 图层分组列表
├── 多数据源加载
├── 智能样式编辑
└── 字段标注配置

MapControls.vue - 简洁的地图控件
├── 放大/缩小
├── 定位
└── 主页
```

### 样式特性
- 现代化的渐变背景和阴影效果
- 响应式设计，支持移动端
- 毛玻璃效果和动画过渡
- 主题色区分不同功能组
- 悬停效果和状态反馈

## 注意事项

1. **性能优化**
   - 大量图层时建议使用分组管理
   - 适当调节透明度以提高渲染性能

2. **兼容性**
   - 确保Cesium地图引擎正确初始化
   - 图层管理器需要在地图加载完成后初始化

3. **用户体验**
   - 建议先添加图层再使用快捷操作
   - 样式预设仅对相应类型的图层生效

## 更新日志

### v1.1.0 (当前版本)
- 优化工具栏按钮分组，图层管理作为GIS应用的子功能
- 移除重复的快捷操作功能，专注于专业图层管理
- 简化地图控件，保留核心地图操作功能
- 增强图层管理面板的专业性和易用性
- 优化用户界面，减少功能重复
