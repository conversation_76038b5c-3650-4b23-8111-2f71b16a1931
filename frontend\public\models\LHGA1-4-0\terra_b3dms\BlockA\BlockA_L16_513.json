{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.2378435134887695, "root": {"boundingVolume": {"box": [10749.1171875, 3294.74853515625, -510.41046142578125, 167.2333984375, 0.0, 0.0, 0.0, 179.2847900390625, 0.0, 0.0, 0.0, 123.97615051269531]}, "children": [{"boundingVolume": {"box": [10749.1171875, 3205.106201171875, -526.4386596679688, 167.2333984375, 0.0, 0.0, 0.0, 89.642333984375, 0.0, 0.0, 0.0, 103.44123840332031]}, "children": [{"boundingVolume": {"box": [10665.5, 3205.106201171875, -552.4456787109375, 83.61669921875, 0.0, 0.0, 0.0, 89.642333984375, 0.0, 0.0, 0.0, 77.43418884277344]}, "children": [{"boundingVolume": {"box": [10665.5, 3167.75537109375, -577.9925537109375, 83.61669921875, 0.0, 0.0, 0.0, 52.2913818359375, 0.0, 0.0, 0.0, 50.9703369140625]}, "content": {"uri": "BlockA_L18_183.b3dm"}, "geometricError": 0.18157558143138885, "refine": "REPLACE"}, {"boundingVolume": {"box": [10665.5, 3257.3974609375, -525.03369140625, 83.61669921875, 0.0, 0.0, 0.0, 37.3509521484375, 0.0, 0.0, 0.0, 49.945281982421875]}, "content": {"uri": "BlockA_L19_278.b3dm"}, "geometricError": 0.16784532368183136, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1974.b3dm"}, "geometricError": 0.34878483414649963, "refine": "REPLACE"}, {"boundingVolume": {"box": [10832.734375, 3205.106201171875, -513.7244873046875, 83.61669921875, 0.0, 0.0, 0.0, 89.642333984375, 0.0, 0.0, 0.0, 90.72709655761719]}, "children": [{"boundingVolume": {"box": [10832.734375, 3186.777587890625, -557.9884033203125, 83.61669921875, 0.0, 0.0, 0.0, 71.313720703125, 0.0, 0.0, 0.0, 44.996978759765625]}, "content": {"uri": "BlockA_L19_277.b3dm"}, "geometricError": 0.17343273758888245, "refine": "REPLACE"}, {"boundingVolume": {"box": [10827.7734375, 3205.106201171875, -467.994384765625, 78.65625, 0.0, 0.0, 0.0, 89.642333984375, 0.0, 0.0, 0.0, 44.99699401855469]}, "content": {"uri": "BlockA_L19_276.b3dm"}, "geometricError": 0.15665943920612335, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1973.b3dm"}, "geometricError": 0.32844120264053345, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1021.b3dm"}, "geometricError": 0.6774962544441223, "refine": "REPLACE"}, {"boundingVolume": {"box": [10748.416015625, 3384.39111328125, -461.6029968261719, 166.53271484375, 0.0, 0.0, 0.0, 89.6424560546875, 0.0, 0.0, 0.0, 75.07101440429688]}, "children": [{"boundingVolume": {"box": [10651.2724609375, 3384.39111328125, -464.6357116699219, 69.388671875, 0.0, 0.0, 0.0, 89.6424560546875, 0.0, 0.0, 0.0, 72.03829956054688]}, "children": [{"boundingVolume": {"box": [10651.2724609375, 3339.56982421875, -475.58050537109375, 69.388671875, 0.0, 0.0, 0.0, 44.8212890625, 0.0, 0.0, 0.0, 60.65919494628906]}, "content": {"uri": "BlockA_L19_275.b3dm"}, "geometricError": 0.15245787799358368, "refine": "REPLACE"}, {"boundingVolume": {"box": [10616.578125, 3429.21240234375, -422.1463623046875, 34.6943359375, 0.0, 0.0, 0.0, 44.8211669921875, 0.0, 0.0, 0.0, 29.484054565429688]}, "content": {"uri": "BlockA_L19_274.b3dm"}, "geometricError": 0.13693612813949585, "refine": "REPLACE"}, {"boundingVolume": {"box": [10685.966796875, 3429.21240234375, -427.4893798828125, 34.6943359375, 0.0, 0.0, 0.0, 44.8211669921875, 0.0, 0.0, 0.0, 34.82708740234375]}, "content": {"uri": "BlockA_L19_273.b3dm"}, "geometricError": 0.13887563347816467, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1972.b3dm"}, "geometricError": 0.289191871881485, "refine": "REPLACE"}, {"boundingVolume": {"box": [10817.8046875, 3384.39111328125, -447.81878662109375, 97.14404296875, 0.0, 0.0, 0.0, 89.6424560546875, 0.0, 0.0, 0.0, 61.07220458984375]}, "children": [{"boundingVolume": {"box": [10761.1376953125, 3339.56982421875, -440.5286865234375, 40.4765625, 0.0, 0.0, 0.0, 44.8212890625, 0.0, 0.0, 0.0, 53.7821044921875]}, "content": {"uri": "BlockA_L19_272.b3dm"}, "geometricError": 0.14491447806358337, "refine": "REPLACE"}, {"boundingVolume": {"box": [10761.1376953125, 3429.21240234375, -427.03851318359375, 40.4765625, 0.0, 0.0, 0.0, 44.8211669921875, 0.0, 0.0, 0.0, 39.48887634277344]}, "content": {"uri": "BlockA_L19_271.b3dm"}, "geometricError": 0.1398298442363739, "refine": "REPLACE"}, {"boundingVolume": {"box": [10858.28125, 3384.39111328125, -447.19183349609375, 56.66748046875, 0.0, 0.0, 0.0, 89.6424560546875, 0.0, 0.0, 0.0, 60.44525146484375]}, "content": {"uri": "BlockA_L19_270.b3dm"}, "geometricError": 0.14163753390312195, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_1971.b3dm"}, "geometricError": 0.28474998474121094, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1020.b3dm"}, "geometricError": 0.5747351050376892, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_513.b3dm"}, "geometricError": 1.2378435134887695, "refine": "REPLACE"}}