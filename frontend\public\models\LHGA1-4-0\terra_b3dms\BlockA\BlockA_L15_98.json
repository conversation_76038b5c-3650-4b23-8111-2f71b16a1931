{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.4703353643417358, "root": {"boundingVolume": {"box": [4971.3310546875, 2903.128662109375, -743.8300170898438, 212.845458984375, 0.0, 0.0, 0.0, 128.091552734375, 0.0, 0.0, 0.0, 183.877685546875]}, "children": [{"boundingVolume": {"box": [4864.908203125, 2903.128662109375, -812.175537109375, 106.4228515625, 0.0, 0.0, 0.0, 128.091552734375, 0.0, 0.0, 0.0, 115.46151733398438]}, "children": [{"boundingVolume": {"box": [4864.908203125, 2839.0830078125, -811.275634765625, 106.4228515625, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 114.24508666992188]}, "children": [{"boundingVolume": {"box": [4831.17578125, 2839.0830078125, -868.3348388671875, 72.690185546875, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 57.101409912109375]}, "content": {"uri": "BlockA_L18_746.b3dm"}, "geometricError": 0.21821263432502747, "refine": "REPLACE"}, {"boundingVolume": {"box": [4899.86669921875, 2839.0830078125, -754.1319580078125, 71.46435546875, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 57.1014404296875]}, "content": {"uri": "BlockA_L18_745.b3dm"}, "geometricError": 0.19129611551761627, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_407.b3dm"}, "geometricError": 0.4070022702217102, "refine": "REPLACE"}, {"boundingVolume": {"box": [4864.908203125, 2967.17431640625, -828.7982177734375, 106.4228515625, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 75.389404296875]}, "children": [{"boundingVolume": {"box": [4811.69677734375, 2967.17431640625, -830.9295654296875, 53.21142578125, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 73.2047119140625]}, "content": {"uri": "BlockA_L18_744.b3dm"}, "geometricError": 0.21122848987579346, "refine": "REPLACE"}, {"boundingVolume": {"box": [4918.11962890625, 2967.17431640625, -808.42529296875, 53.21142578125, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 55.016448974609375]}, "content": {"uri": "BlockA_L18_743.b3dm"}, "geometricError": 0.20272035896778107, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_406.b3dm"}, "geometricError": 0.4142909348011017, "refine": "REPLACE"}], "content": {"uri": "BlockA_L16_204.b3dm"}, "geometricError": 0.8207728266716003, "refine": "REPLACE"}, {"boundingVolume": {"box": [5077.75390625, 2903.128662109375, -670.7028198242188, 106.422607421875, 0.0, 0.0, 0.0, 128.091552734375, 0.0, 0.0, 0.0, 110.04803466796875]}, "children": [{"boundingVolume": {"box": [5024.54248046875, 2839.0830078125, -701.6493530273438, 53.21142578125, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 61.01666259765625]}, "children": [{"boundingVolume": {"box": [5024.54248046875, 2839.0830078125, -702.85498046875, 53.21142578125, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 59.81103515625]}, "content": {"uri": "BlockA_L19_565.b3dm"}, "geometricError": 0.17138509452342987, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2122.b3dm"}, "geometricError": 0.34290289878845215, "refine": "REPLACE"}, {"boundingVolume": {"box": [5130.96484375, 2839.0830078125, -691.331787109375, 53.211181640625, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 74.826904296875]}, "children": [{"boundingVolume": {"box": [5130.96484375, 2829.35498046875, -722.5096435546875, 53.211181640625, 0.0, 0.0, 0.0, 54.3177490234375, 0.0, 0.0, 0.0, 43.649017333984375]}, "content": {"uri": "BlockA_L19_564.b3dm"}, "geometricError": 0.17462553083896637, "refine": "REPLACE"}, {"boundingVolume": {"box": [5130.96484375, 2852.22900390625, -647.6827392578125, 53.211181640625, 0.0, 0.0, 0.0, 50.899658203125, 0.0, 0.0, 0.0, 31.177886962890625]}, "content": {"uri": "BlockA_L19_563.b3dm"}, "geometricError": 0.16072522103786469, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2121.b3dm"}, "geometricError": 0.3342853784561157, "refine": "REPLACE"}, {"boundingVolume": {"box": [5077.75390625, 2967.17431640625, -670.9019775390625, 106.422607421875, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 109.50308227539062]}, "children": [{"boundingVolume": {"box": [5024.55126953125, 2967.17431640625, -725.6535034179688, 53.22021484375, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 54.75152587890625]}, "content": {"uri": "BlockA_L18_742.b3dm"}, "geometricError": 0.1786150336265564, "refine": "REPLACE"}, {"boundingVolume": {"box": [5083.83154296875, 2967.17431640625, -643.1598510742188, 33.4482421875, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 27.74212646484375]}, "content": {"uri": "BlockA_L19_562.b3dm"}, "geometricError": 0.1590549647808075, "refine": "REPLACE"}, {"boundingVolume": {"box": [5150.72802734375, 2967.17431640625, -611.892333984375, 33.4482421875, 0.0, 0.0, 0.0, 64.0457763671875, 0.0, 0.0, 0.0, 50.493499755859375]}, "content": {"uri": "BlockA_L19_561.b3dm"}, "geometricError": 0.14823304116725922, "refine": "REPLACE"}], "content": {"uri": "BlockA_L18_2120.b3dm"}, "geometricError": 0.327116996049881, "refine": "REPLACE"}], "content": {"uri": "BlockA_L17_1095.b3dm"}, "geometricError": 0.66645747423172, "refine": "REPLACE"}], "content": {"uri": "BlockA_L15_98.b3dm"}, "geometricError": 1.4703353643417358, "refine": "REPLACE"}}