import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(LocalStrategy.name);

  constructor(private authService: AuthService) {
    super({
      usernameField: 'username',
      passwordField: 'password',
    });
  }

  async validate(username: string, password: string): Promise<any> {
    this.logger.log(`=== LOCAL STRATEGY VALIDATE === Username: ${username}`);
    const user = await this.authService.validateUser(username, password);
    
    if (!user) {
      this.logger.error(`=== VALIDATION FAILED === Username: ${username}`);
      throw new UnauthorizedException('用户名或密码错误');
    }
    
    this.logger.log(`=== VALIDATION SUCCESS === Username: ${username}`);
    return user;
  }
}